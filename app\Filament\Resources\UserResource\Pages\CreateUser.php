<?php

namespace App\Filament\Resources\UserResource\Pages;

use App\Filament\Resources\UserResource;
use Filament\Resources\Pages\CreateRecord;
use Spatie\Permission\Models\Role;

class CreateUser extends CreateRecord
{
    protected static string $resource = UserResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        return $data;
    }

    protected function afterCreate(): void
    {
        // Pastikan relasi roles disimpan dengan benar
        $record = $this->getRecord();

        if ($record && isset($this->data['roles']) && !empty($this->data['roles'])) {
            // Konversi role IDs ke role names
            $roleNames = Role::whereIn('id', $this->data['roles'])->pluck('name')->toArray();

            // Sync roles menggunakan nama role
            $record->syncRoles($roleNames);

            // Refresh model untuk memuat relasi yang baru
            $record->refresh();
        }
    }
}
