<?php

namespace App\Filament\Resources\UserResource\Pages;

use App\Filament\Resources\UserResource;
use Filament\Resources\Pages\CreateRecord;

class CreateUser extends CreateRecord
{
    protected static string $resource = UserResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        return $data;
    }

    protected function afterCreate(): void
    {
        // Pastikan relasi roles disimpan dengan benar
        $record = $this->getRecord();

        if ($record && isset($this->data['roles'])) {
            // Sync roles menggunakan Spatie Permission langsung
            $record->syncRoles($this->data['roles']);

            // Refresh model untuk memuat relasi yang baru
            $record->refresh();
        }
    }
}
