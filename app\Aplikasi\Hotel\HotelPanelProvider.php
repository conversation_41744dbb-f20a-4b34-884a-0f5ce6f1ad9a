<?php

namespace App\Aplikasi\Hotel;

use App\Aplikasi\Hotel\Pages\Dashboard;
use App\Aplikasi\Hotel\Resources\KamarResource;
use App\Aplikasi\Hotel\Resources\KonfigResource;
use App\Aplikasi\Hotel\Resources\LaporanResource;
use App\Aplikasi\Hotel\Resources\PembayaranResource;
use App\Aplikasi\Hotel\Resources\ReservasiResource;
use App\Aplikasi\Hotel\Resources\TamuResource;
use App\Aplikasi\Hotel\Widgets\CalendarWidget;
use App\Filament\Resources\KonfigUtamaResource;
use App\Filament\Resources\MetodePembayaranUtamaResource;
use App\Models\MetodePembayaranUtama;
use App\Models\User;
use BezhanSalleh\FilamentShield\FilamentShieldPlugin;
use DutchCodingCompany\FilamentDeveloperLogins\FilamentDeveloperLoginsPlugin;
use Filament\Http\Middleware\Authenticate;
use Filament\Http\Middleware\DisableBladeIconComponents;
use Filament\Http\Middleware\DispatchServingFilamentEvent;
use Filament\Navigation\MenuItem;
use Filament\Navigation\NavigationBuilder;
use Filament\Navigation\NavigationGroup;
use Filament\Navigation\NavigationItem;
use Filament\Panel;
use Filament\PanelProvider;
use Filament\Support\Assets\Css;
use Filament\Support\Colors\Color;
use Filament\Support\Facades\FilamentAsset;
use Guava\Tutorials\TutorialsPlugin;
use Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse;
use Illuminate\Cookie\Middleware\EncryptCookies;
use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken;
use Illuminate\Routing\Middleware\SubstituteBindings;
use Illuminate\Session\Middleware\AuthenticateSession;
use Illuminate\Session\Middleware\StartSession;
use Illuminate\Support\Facades\Auth;
use Illuminate\View\Middleware\ShareErrorsFromSession;
use Jeffgreco13\FilamentBreezy\BreezyCore;
use Saade\FilamentFullCalendar\FilamentFullCalendarPlugin;

class HotelPanelProvider extends PanelProvider
{
    public function panel(Panel $panel): Panel
    {
        return $panel
            ->id('hotel')
            ->path('adminhotel')
            ->login()
            ->topNavigation()
            ->pages([
                Dashboard::class,
            ])
            ->colors([
                'primary' => Color::Blue,
                'danger' => Color::Rose,
                'success' => Color::Emerald,
                'warning' => Color::Orange,
            ])
            ->globalSearch(false)
            ->viteTheme('resources/css/filament/hotel/theme.css')
            ->navigationGroups([
                NavigationGroup::make('Reservasi'),
                NavigationGroup::make('Laporan'),
                NavigationGroup::make('Pengaturan'),
            ])
            ->navigationItems([
                // Dashboard
                NavigationItem::make('Dashboard')
                    ->icon('heroicon-o-home')
                    ->isActiveWhen(fn(): bool => request()->routeIs('filament.hotel.pages.dashboard-karyawan'))
                    ->url(fn(): string => Dashboard::getUrl())
                    ->sort(1),

                // Grup Reservasi
                NavigationItem::make('List Reservasi')
                    ->icon('heroicon-o-list-bullet')
                    ->url(fn(): string => ReservasiResource::getUrl('index'))
                    ->visible($this->cekAkses('adminhotel.reservasi'))
                    ->group('Reservasi')
                    ->sort(1),
                NavigationItem::make('Buat Reservasi')
                    ->icon('heroicon-o-plus-circle')
                    ->visible($this->cekAkses('adminhotel.reservasi'))
                    ->url(fn(): string => ReservasiResource::getUrl('create'))
                    ->group('Reservasi')
                    ->sort(2),

                NavigationItem::make('Daftar Tamu')
                    ->icon('heroicon-o-user-group')
                    ->visible($this->cekAkses('adminhotel.tamu'))
                    ->url(fn(): string => TamuResource::getUrl('index'))
                    ->group('Reservasi')
                    ->sort(3),

                NavigationItem::make('Laporan Reservasi')
                    ->icon('heroicon-o-document-chart-bar')
                    ->visible($this->cekAkses('adminhotel.laporan'))
                    ->url(fn(): string => LaporanResource::getUrl('index'))
                    ->group('Laporan')
                    ->sort(3),

                NavigationItem::make('Laporan Pembayaran')
                    ->icon('heroicon-o-banknotes')
                    ->visible($this->cekAkses('adminhotel.laporan'))
                    ->url(fn(): string => PembayaranResource::getUrl('index'))
                    ->group('Laporan')
                    ->sort(3),

                NavigationItem::make('Fasilitas')
                    ->icon('tabler-gymnastics')
                    ->visible($this->cekAkses('adminhotel.pengaturan.fasilitas'))
                    ->url(fn(): string => \App\Aplikasi\Hotel\Resources\FasilitasResource::getUrl('index'))
                    ->group('Pengaturan')
                    ->sort(3),

                NavigationItem::make('Tipe kamar')
                    ->icon('heroicon-o-building-storefront')
                    ->visible($this->cekAkses('adminhotel.pengaturan.kamar'))
                    ->url(fn(): string =>  \App\Aplikasi\Hotel\Resources\TipeKamarResource::getUrl('index'))
                    ->group('Pengaturan')
                    ->sort(3),

                NavigationItem::make('kamar')
                    ->icon('heroicon-o-home')
                    ->visible($this->cekAkses('adminhotel.pengaturan.kamar'))
                    ->url(fn(): string =>  KamarResource::getUrl('index'))
                    ->group('Pengaturan')
                    ->sort(3),

                NavigationItem::make('Data manager')
                    ->icon('heroicon-o-circle-stack')
                    ->visible($this->cekAkses('adminhotel.pengaturan.data'))
                    ->url(fn(): string =>   \App\Aplikasi\Hotel\Pages\DataPage::getUrl())
                    ->group('Pengaturan')
                    ->sort(3),

                NavigationItem::make('Konfig')
                    ->icon('heroicon-o-cog-6-tooth')
                    ->visible($this->cekAkses('adminhotel.pengaturan.konfig'))
                    ->url(fn(): string => KonfigResource::getUrl('index'))
                    ->group('Pengaturan')
                    ->sort(3),








            ])

            ->discoverResources(in: __DIR__ . '/Resources', for: 'App\\Aplikasi\\Hotel\\Resources')
            ->discoverPages(in: __DIR__ . '/Pages', for: 'App\\Aplikasi\\Hotel\\Pages')
            ->discoverWidgets(in: __DIR__ . '/Widgets', for: 'App\\Aplikasi\\Hotel\\Widgets')
            ->widgets([])
            ->resources([
                MetodePembayaranUtamaResource::class,
                KonfigResource::class,
            ])
            ->discoverLivewireComponents(in: __DIR__ . '/Livewire', for: 'App\\Aplikasi\\Hotel\\Livewire')
            ->middleware([
                // \App\Http\Middleware\BypassAuth::class, // DISABLED - Bypass autentikasi untuk development
                EncryptCookies::class,
                AddQueuedCookiesToResponse::class,
                StartSession::class,
                AuthenticateSession::class,
                ShareErrorsFromSession::class,
                VerifyCsrfToken::class,
                SubstituteBindings::class,
                DisableBladeIconComponents::class,
                DispatchServingFilamentEvent::class,
            ])
            ->authMiddleware([
                Authenticate::class,
             
            ])
            // ->authGuard('customers')
            ->plugins([
   
                FilamentDeveloperLoginsPlugin::make()
                    ->enabled()
                    ->users(fn() => User::pluck('email', 'name')->toArray()),
                BreezyCore::make()
                    ->myProfile(
                        shouldRegisterUserMenu: false, // Sets the 'account' link in the panel User Menu (default = true)
                        userMenuLabel: 'profileku', // Customizes the 'account' link label in the panel User Menu (default = null)
                        shouldRegisterNavigation: false, // Adds a main navigation item for the My Profile page (default = false)
                        navigationGroup: 'Pengaturan', // Sets the navigation group for the My Profile page (default = null)
                        hasAvatars: false, // Enables the avatar upload form component (default = false)
                        slug: 'my-profile' // Sets the slug for the profile page (default = 'my-profile')
                    ),

             


            ])
            ->userMenuItems([
                'Nama' =>   MenuItem::make()
                    ->label(fn() => Auth::user()->name)
                    ->url(fn() => url('/adminhotel/my-profile'))
                    ->icon('heroicon-o-user'),
                // ...
            ])
            ->breadcrumbs(false)
        ;
    }





    protected function cekAkses($permission)
    {
        return fn() => Auth::check() && (Auth::user()->hasRole('superadmin') || Auth::user()->can($permission));
    }
}
