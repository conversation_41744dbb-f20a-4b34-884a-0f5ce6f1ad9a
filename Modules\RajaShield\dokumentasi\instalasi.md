# Instalasi dan Konfigu<PERSON>i RajaShield

## Daftar Isi

1. [Persyaratan Sistem](#persyaratan-sistem)
2. [Instalasi](#instalasi)
3. [Konfigurasi](#konfigurasi)
4. [Migrasi Database](#migrasi-database)
5. [Seeding Data](#seeding-data)
6. [Verifikasi Instalasi](#verifikasi-instalasi)

## Persyaratan Sistem

### Minimum Requirements

- **PHP**: 8.1 atau lebih tinggi
- **Laravel**: 11.x
- **FilamentPHP**: 3.2.x
- **Database**: MySQL 8.0+ / PostgreSQL 13+ / SQLite 3.35+
- **Memory**: 512MB RAM minimum
- **Storage**: 50MB disk space

### Dependencies

Modul RajaShield memerlukan package berikut:

```json
{
    "spatie/laravel-permission": "^6.0",
    "filament/filament": "^3.2",
    "nwidart/laravel-modules": "^10.0"
}
```

## Instalasi

### Langkah 1: Persiapan Environment

Pastikan environment Laravel Anda sudah siap:

```bash
# Cek versi PHP
php --version

# Cek versi Laravel
php artisan --version

# Cek status database
php artisan migrate:status
```

### Langkah 2: Install Dependencies

Jika belum terinstall, install dependencies yang diperlukan:

```bash
# Install Spatie Laravel Permission
composer require spatie/laravel-permission

# Install FilamentPHP (jika belum ada)
composer require filament/filament:"^3.2"

# Install Laravel Modules (jika belum ada)
composer require nwidart/laravel-modules
```

### Langkah 3: Publish Spatie Permission

```bash
# Publish migration Spatie Permission
php artisan vendor:publish --provider="Spatie\Permission\PermissionServiceProvider"

# Jalankan migration Spatie Permission
php artisan migrate
```

### Langkah 4: Enable Modul RajaShield

```bash
# Enable modul
php artisan module:enable RajaShield

# Verify modul status
php artisan module:list
```

### Langkah 5: Konfigurasi User Model

Tambahkan trait `HasRoles` ke User model Anda:

```php
<?php

namespace App\Models;

use Illuminate\Foundation\Auth\User as Authenticatable;
use Spatie\Permission\Traits\HasRoles;

class User extends Authenticatable
{
    use HasRoles;
    
    // ... rest of your model
}
```

## Konfigurasi

### Konfigurasi Spatie Permission

Edit file `config/permission.php`:

```php
<?php

return [
    'models' => [
        'permission' => Modules\RajaShield\Models\Permission::class,
        'role' => Modules\RajaShield\Models\Role::class,
    ],

    'table_names' => [
        'roles' => 'roles',
        'permissions' => 'permissions',
        'model_has_permissions' => 'model_has_permissions',
        'model_has_roles' => 'model_has_roles',
        'role_has_permissions' => 'role_has_permissions',
    ],

    'column_names' => [
        'role_pivot_key' => null,
        'permission_pivot_key' => null,
        'model_morph_key' => 'model_id',
        'team_foreign_key' => 'team_id',
    ],

    'register_permission_check_method' => true,
    'register_octane_reset_listener' => false,
    'teams' => false,
    'use_passport_client_credentials' => false,
    'display_permission_in_exception' => false,
    'display_role_in_exception' => false,
    'enable_wildcard_permission' => false,
    'cache' => [
        'expiration_time' => \DateInterval::createFromDateString('24 hours'),
        'key' => 'spatie.permission.cache',
        'store' => 'default',
    ],
];
```

### Konfigurasi FilamentPHP Panel

Jika menggunakan custom panel, daftarkan resources RajaShield:

```php
<?php

namespace App\Providers\Filament;

use Filament\Panel;
use Filament\PanelProvider;
use Modules\RajaShield\Filament\Resources\RoleResource;
use Modules\RajaShield\Filament\Resources\PermissionResource;
use Modules\RajaShield\Filament\Pages\SyncPermissions;

class AdminPanelProvider extends PanelProvider
{
    public function panel(Panel $panel): Panel
    {
        return $panel
            ->default()
            ->id('admin')
            ->path('admin')
            ->resources([
                RoleResource::class,
                PermissionResource::class,
            ])
            ->pages([
                SyncPermissions::class,
            ]);
    }
}
```

## Migrasi Database

### Langkah 1: Jalankan Migrasi RajaShield

```bash
# Jalankan migrasi modul
php artisan module:migrate RajaShield

# Atau jalankan migrasi specific
php artisan migrate --path=Modules/RajaShield/database/migrations
```

### Langkah 2: Verifikasi Tabel

Pastikan tabel-tabel berikut sudah terbuat:

- `roles` - Menyimpan data role
- `permissions` - Menyimpan data permission
- `model_has_roles` - Relasi user-role
- `model_has_permissions` - Relasi user-permission
- `role_has_permissions` - Relasi role-permission

```bash
# Cek tabel di database
php artisan db:show --table=roles
php artisan db:show --table=permissions
```

## Seeding Data

### Langkah 1: Jalankan Seeder

```bash
# Jalankan seeder RajaShield
php artisan module:seed RajaShield

# Atau jalankan seeder specific
php artisan db:seed --class="Modules\RajaShield\Database\Seeders\RajaShieldSeeder"
```

### Langkah 2: Data Default yang Dibuat

Seeder akan membuat:

#### Roles Default:
- **super-admin**: Administrator dengan semua permission
- **admin**: Administrator dengan permission terbatas
- **user**: User biasa tanpa permission khusus

#### Permissions Default:
- Permissions untuk semua FilamentPHP Resources
- Permissions untuk semua FilamentPHP Pages
- Permissions untuk semua FilamentPHP Widgets

### Langkah 3: Assign Role ke User

```bash
# Buat user super admin (opsional)
php artisan tinker
```

```php
// Di dalam tinker
$user = \App\Models\User::find(1); // Ganti dengan ID user Anda
$user->assignRole('super-admin');
```

## Verifikasi Instalasi

### Langkah 1: Cek Status Modul

```bash
# Cek status modul
php artisan module:list

# Output yang diharapkan:
# +----------+---------+---------+----------+
# | Name     | Status  | Order   | Path     |
# +----------+---------+---------+----------+
# | RajaShield | Enabled | 0       | Modules/RajaShield |
# +----------+---------+---------+----------+
```

### Langkah 2: Test Sync Permissions

```bash
# Test sync permissions
php artisan rajashield:sync-permissions --dry-run

# Output yang diharapkan menunjukkan permissions yang terdeteksi
```

### Langkah 3: Akses Admin Panel

1. Login ke FilamentPHP admin panel
2. Cek menu **RajaShield** tersedia
3. Akses **Roles** dan **Permissions** resources
4. Akses halaman **Sync Permissions**

### Langkah 4: Test Permissions

```php
// Test di tinker atau controller
$user = auth()->user();

// Cek role
$user->hasRole('super-admin'); // true/false

// Cek permission
$user->can('view_any_roles'); // true/false

// Assign permission
$user->givePermissionTo('view_any_permissions');
```

## Troubleshooting Instalasi

### Error: Class not found

```bash
# Clear cache
php artisan config:clear
php artisan cache:clear
php artisan route:clear

# Regenerate autoload
composer dump-autoload
```

### Error: Migration failed

```bash
# Rollback dan coba lagi
php artisan migrate:rollback --step=1
php artisan module:migrate RajaShield
```

### Error: Permission denied

```bash
# Set permission folder
chmod -R 755 Modules/RajaShield
chown -R www-data:www-data Modules/RajaShield
```

### Error: Seeder failed

```bash
# Truncate tables dan seed ulang
php artisan db:seed --class="Modules\RajaShield\Database\Seeders\RajaShieldSeeder" --force
```

## Konfigurasi Lanjutan

### Custom Permission Actions

Edit `RajaShieldPermissionManager` untuk custom actions:

```php
// Modules/RajaShield/app/Helpers/RajaShieldPermissionManager.php
const RESOURCE_PERMISSION_ACTIONS = [
    'view',
    'view_any', 
    'create',
    'update',
    'delete',
    'delete_any',
    'export', // Custom action
    'import', // Custom action
];
```

### Custom Guards

Konfigurasi multiple guards di `config/permission.php`:

```php
'guards' => ['web', 'api', 'admin'],
```

Instalasi RajaShield sekarang sudah selesai! Lanjutkan ke [Panduan Penggunaan](penggunaan.md) untuk mempelajari cara menggunakan semua fitur.
