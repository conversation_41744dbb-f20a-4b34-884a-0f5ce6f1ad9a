# FAQ (Frequently Asked Questions) - RajaShield

## Daftar Isi

1. [Per<PERSON>aan Umum](#pertanyaan-umum)
2. [Instalasi & Konfigurasi](#instalasi--konfigurasi)
3. [Penggunaan](#penggunaan)
4. [Performance & Optimization](#performance--optimization)
5. [Troubleshooting](#troubleshooting)
6. [Best Practices](#best-practices)

## Pertanyaan Umum

### Q: Apa itu RajaShield?
**A**: RajaShield adalah modul Laravel untuk mengelola sistem role dan permission menggunakan Spatie Laravel-Permission. Modul ini dirancang khusus untuk integrasi dengan FilamentPHP dan menyediakan interface yang user-friendly untuk manajemen hak akses.

### Q: Apakah RajaShield gratis?
**A**: Ya, RajaShield adalah open-source software yang dilisensikan di bawah MIT License.

### Q: Apakah RajaShield kompatibel dengan Laravel 11?
**A**: Ya, RajaShield dirancang khusus untuk Laravel 11 dan FilamentPHP 3.2.

### Q: Bisakah RajaShield digunakan tanpa FilamentPHP?
**A**: Secara teknis bisa, karena RajaShield menggunakan Spatie Permission sebagai base. Namun, fitur utama RajaShield adalah integrasi dengan FilamentPHP, jadi penggunaan tanpa FilamentPHP tidak disarankan.

### Q: Apakah RajaShield mendukung multi-tenant?
**A**: Ya, RajaShield mendukung multi-tenant melalui fitur teams dari Spatie Permission. Anda perlu mengaktifkan fitur teams di konfigurasi.

## Instalasi & Konfigurasi

### Q: Bagaimana cara install RajaShield?
**A**: 
```bash
# Enable modul
php artisan module:enable RajaShield

# Jalankan migrasi
php artisan module:migrate RajaShield

# Jalankan seeder
php artisan module:seed RajaShield

# Sync permissions
php artisan rajashield:sync-permissions
```

### Q: Error "Class not found" saat instalasi, bagaimana solusinya?
**A**: 
```bash
# Refresh autoloader
composer dump-autoload

# Clear cache
php artisan config:clear
php artisan cache:clear
```

### Q: Apakah perlu konfigurasi khusus untuk User model?
**A**: Ya, tambahkan trait `HasRoles` ke User model:
```php
use Spatie\Permission\Traits\HasRoles;

class User extends Authenticatable
{
    use HasRoles;
    // ...
}
```

### Q: Bagaimana cara mengubah guard default?
**A**: Edit file `config/permission.php`:
```php
'defaults' => [
    'guard' => 'web', // Ubah sesuai kebutuhan
    'passwords' => 'users',
],
```

### Q: Bisakah menggunakan database selain MySQL?
**A**: Ya, RajaShield mendukung PostgreSQL, SQLite, dan database lain yang didukung Laravel.

## Penggunaan

### Q: Bagaimana cara membuat role baru?
**A**: 
```php
use Modules\RajaShield\Models\Role;

$role = Role::create([
    'name' => 'editor',
    'description' => 'Content Editor',
]);
```

### Q: Bagaimana cara assign role ke user?
**A**: 
```php
$user = User::find(1);
$user->assignRole('editor');

// Atau multiple roles
$user->assignRole(['editor', 'moderator']);
```

### Q: Bagaimana cara cek permission di controller?
**A**: 
```php
// Menggunakan can()
if (auth()->user()->can('edit_posts')) {
    // User dapat edit posts
}

// Menggunakan Gate
Gate::authorize('edit_posts');

// Menggunakan middleware
Route::middleware(['permission:edit_posts'])->group(function () {
    // Routes
});
```

### Q: Bagaimana cara membuat permission custom?
**A**: 
```php
use Modules\RajaShield\Models\Permission;

$permission = Permission::create([
    'name' => 'manage_settings',
    'description' => 'Manage application settings',
    'group' => 'system',
]);
```

### Q: Kapan harus menjalankan sync permissions?
**A**: Jalankan sync permissions setiap kali:
- Menambah Resource/Page/Widget baru
- Menghapus Resource/Page/Widget
- Setelah update modul atau aplikasi
- Setelah deployment

### Q: Bagaimana cara membuat super admin?
**A**: 
```php
$user = User::find(1);
$user->assignRole('super-admin');

// Super admin otomatis mendapat semua permissions
```

## Performance & Optimization

### Q: Bagaimana cara mengoptimalkan performa permission?
**A**: 
1. **Gunakan cache**:
```php
$permissions = Cache::remember(
    "user.{$userId}.permissions",
    3600,
    fn() => $user->getAllPermissions()
);
```

2. **Eager loading**:
```php
$users = User::with('roles.permissions')->get();
```

3. **Index database**:
```sql
CREATE INDEX idx_permissions_name ON permissions(name);
```

### Q: Apakah ada limit jumlah role/permission?
**A**: Tidak ada limit hard-coded, tapi untuk performa optimal disarankan:
- Maksimal 50 roles per aplikasi
- Maksimal 500 permissions per aplikasi
- Maksimal 10 roles per user

### Q: Bagaimana cara mengurangi memory usage saat sync?
**A**: 
```bash
# Increase memory limit
php -d memory_limit=512M artisan rajashield:sync-permissions
```

## Troubleshooting

### Q: Permission tidak muncul di form role, kenapa?
**A**: 
1. Jalankan sync permissions:
```bash
php artisan rajashield:sync-permissions --force
```

2. Clear cache:
```bash
php artisan cache:clear
php artisan permission:cache-reset
```

### Q: User sudah ada permission tapi masih tidak bisa akses?
**A**: 
1. Cek cache permission:
```bash
php artisan permission:cache-reset
```

2. Cek implementasi permission di Resource:
```php
public static function canViewAny(): bool
{
    return auth()->user()->can('view_any_posts');
}
```

### Q: Error "This action is unauthorized"?
**A**: 
1. Cek permission user:
```php
dd(auth()->user()->getAllPermissions()->pluck('name'));
```

2. Assign permission yang diperlukan:
```php
auth()->user()->givePermissionTo('required_permission');
```

### Q: Sync permissions tidak mendeteksi Resource baru?
**A**: 
1. Clear compiled files:
```bash
php artisan clear-compiled
php artisan config:clear
```

2. Cek apakah Resource dapat di-load:
```php
// Di tinker
class_exists('App\\Filament\\Resources\\NewResource');
```

## Best Practices

### Q: Apa naming convention yang disarankan?
**A**: 
- **Roles**: kebab-case (`super-admin`, `content-manager`)
- **Permissions**: snake_case (`view_any_posts`, `create_posts`)
- **Groups**: singular, lowercase (`resource`, `page`, `widget`)

### Q: Bagaimana struktur role hierarchy yang baik?
**A**: 
```
super-admin (semua permissions)
├── admin (management permissions)
├── manager (department-specific permissions)
├── editor (content permissions)
└── user (basic permissions)
```

### Q: Kapan menggunakan direct permission vs role?
**A**: 
- **Role**: Untuk grouping permissions yang sering digunakan bersama
- **Direct Permission**: Untuk permission khusus yang jarang digunakan

### Q: Bagaimana cara handle permission untuk multi-tenant?
**A**: 
```php
// Enable teams di config/permission.php
'teams' => true,

// Assign role dengan team context
$user->assignRole('admin', $team);
```

### Q: Bagaimana cara backup/restore permissions?
**A**: 
```bash
# Export permissions
php artisan db:seed --class="PermissionExportSeeder"

# Import permissions
php artisan db:seed --class="PermissionImportSeeder"
```

### Q: Apakah perlu membuat permission untuk setiap action?
**A**: Tidak selalu. Buat permission yang granular tapi tidak berlebihan:
- ✅ `view_posts`, `create_posts`, `edit_posts`
- ❌ `view_post_title`, `edit_post_content`, `change_post_color`

### Q: Bagaimana cara testing permissions?
**A**: 
```php
// Feature test
public function test_admin_can_view_posts()
{
    $admin = User::factory()->create();
    $admin->assignRole('admin');

    $response = $this->actingAs($admin)
        ->get('/admin/posts');

    $response->assertStatus(200);
}

// Unit test
public function test_user_has_permission()
{
    $user = User::factory()->create();
    $user->givePermissionTo('view_posts');

    $this->assertTrue($user->can('view_posts'));
}
```

### Q: Bagaimana cara handle permission inheritance?
**A**: 
```php
// Parent role
$adminRole = Role::create(['name' => 'admin']);
$adminRole->givePermissionTo(['view_posts', 'edit_posts']);

// Child role inherit dari parent
$editorRole = Role::create(['name' => 'editor']);
$editorRole->givePermissionTo($adminRole->permissions);
$editorRole->givePermissionTo(['create_posts']); // Additional permission
```

### Q: Bagaimana cara audit permission changes?
**A**: 
```php
// Gunakan Laravel Auditing atau custom logging
use Spatie\Activitylog\Traits\LogsActivity;

class Role extends SpatieRole
{
    use LogsActivity;

    protected static $logAttributes = ['name', 'permissions'];
    protected static $logOnlyDirty = true;
}
```

### Q: Apakah ada tools untuk visualisasi permissions?
**A**: Ya, Anda bisa membuat dashboard custom atau menggunakan package seperti:
- `spatie/laravel-permission-ui`
- Custom FilamentPHP widgets untuk visualisasi

### Q: Bagaimana cara migrate dari sistem permission lain?
**A**: 
1. Buat mapping dari sistem lama ke RajaShield
2. Buat seeder untuk convert data
3. Test secara bertahap
4. Backup data sebelum migration

```php
// Contoh migration seeder
public function run()
{
    $oldPermissions = OldPermission::all();
    
    foreach ($oldPermissions as $oldPerm) {
        Permission::create([
            'name' => $this->convertPermissionName($oldPerm->name),
            'description' => $oldPerm->description,
            'group' => $this->mapGroup($oldPerm->category),
        ]);
    }
}
```

---

**Tidak menemukan jawaban yang Anda cari?** 

Silakan cek dokumentasi lain:
- [Instalasi dan Konfigurasi](instalasi.md)
- [Panduan Penggunaan](penggunaan.md)
- [Troubleshooting](troubleshooting.md)
- [API Reference](api-reference.md)
