# Deployment Guide - RajaShield

Panduan deployment RajaShield untuk production environment.

## Daftar Isi

1. [Pre-deployment Checklist](#pre-deployment-checklist)
2. [Production Setup](#production-setup)
3. [Database Migration](#database-migration)
4. [Permission Sync](#permission-sync)
5. [Performance Optimization](#performance-optimization)
6. [Security Considerations](#security-considerations)
7. [Monitoring & Maintenance](#monitoring--maintenance)

## Pre-deployment Checklist

### ✅ Environment Requirements

```bash
# Cek versi PHP
php --version # Minimal 8.1

# Cek extensions yang diperlukan
php -m | grep -E "(pdo|mbstring|openssl|tokenizer|xml|ctype|json|bcmath)"

# Cek Laravel version
php artisan --version # Minimal 11.x

# Cek FilamentPHP
composer show filament/filament # Minimal 3.2.x
```

### ✅ Dependencies Check

```bash
# Cek semua dependencies terinstall
composer check-platform-reqs

# Verify Spatie Permission
composer show spatie/laravel-permission

# Verify Laravel Modules
composer show nwidart/laravel-modules
```

### ✅ Configuration Validation

```bash
# Test database connection
php artisan migrate:status

# Test cache configuration
php artisan cache:clear

# Test queue configuration (jika menggunakan queue)
php artisan queue:work --once
```

## Production Setup

### 1. Environment Configuration

```bash
# Set environment ke production
APP_ENV=production
APP_DEBUG=false

# Set cache driver yang optimal
CACHE_DRIVER=redis
SESSION_DRIVER=redis
QUEUE_CONNECTION=redis

# Database optimization
DB_CONNECTION=mysql
DB_CHARSET=utf8mb4
DB_COLLATION=utf8mb4_unicode_ci
```

### 2. Install Dependencies

```bash
# Install production dependencies
composer install --optimize-autoloader --no-dev

# Generate application key (jika belum ada)
php artisan key:generate

# Link storage
php artisan storage:link
```

### 3. Enable RajaShield Module

```bash
# Enable modul
php artisan module:enable RajaShield

# Verify module status
php artisan module:list
```

## Database Migration

### 1. Backup Database

```bash
# Backup database sebelum migration
mysqldump -u username -p database_name > backup_$(date +%Y%m%d_%H%M%S).sql

# Atau menggunakan Laravel backup (jika ada)
php artisan backup:run --only-db
```

### 2. Run Migrations

```bash
# Jalankan migrasi Spatie Permission (jika belum)
php artisan migrate --path=vendor/spatie/laravel-permission/database/migrations

# Jalankan migrasi RajaShield
php artisan module:migrate RajaShield

# Verify migration status
php artisan migrate:status
```

### 3. Seed Initial Data

```bash
# Jalankan seeder RajaShield
php artisan module:seed RajaShield

# Atau jalankan seeder specific
php artisan db:seed --class="Modules\RajaShield\Database\Seeders\RajaShieldSeeder"
```

## Permission Sync

### 1. Initial Sync

```bash
# Sync permissions dengan force flag
php artisan rajashield:sync-permissions --force

# Verify sync results
php artisan rajashield:sync-permissions --dry-run
```

### 2. Automated Sync Script

Buat script untuk automated sync:

```bash
#!/bin/bash
# deploy-rajashield.sh

echo "🔄 Starting RajaShield deployment..."

# Clear cache
php artisan cache:clear
php artisan config:clear
php artisan route:clear
php artisan view:clear

# Sync permissions
php artisan rajashield:sync-permissions --force

# Optimize for production
php artisan config:cache
php artisan route:cache
php artisan view:cache

echo "✅ RajaShield deployment completed!"
```

### 3. Add to Deployment Pipeline

```yaml
# .github/workflows/deploy.yml (GitHub Actions)
- name: Sync RajaShield Permissions
  run: |
    php artisan rajashield:sync-permissions --force
    php artisan permission:cache-reset

# atau untuk GitLab CI
rajashield_sync:
  script:
    - php artisan rajashield:sync-permissions --force
    - php artisan permission:cache-reset
```

## Performance Optimization

### 1. Cache Configuration

```php
// config/permission.php
'cache' => [
    'expiration_time' => \DateInterval::createFromDateString('24 hours'),
    'key' => 'spatie.permission.cache',
    'store' => 'redis', // Gunakan Redis untuk production
],
```

### 2. Database Optimization

```sql
-- Tambah index untuk performa
CREATE INDEX idx_permissions_name ON permissions(name);
CREATE INDEX idx_permissions_guard_name ON permissions(guard_name);
CREATE INDEX idx_roles_name ON roles(name);
CREATE INDEX idx_role_has_permissions_role_id ON role_has_permissions(role_id);
CREATE INDEX idx_role_has_permissions_permission_id ON role_has_permissions(permission_id);
CREATE INDEX idx_model_has_roles_model ON model_has_roles(model_type, model_id);
CREATE INDEX idx_model_has_permissions_model ON model_has_permissions(model_type, model_id);
```

### 3. Application Optimization

```bash
# Cache konfigurasi
php artisan config:cache

# Cache routes
php artisan route:cache

# Cache views
php artisan view:cache

# Optimize autoloader
composer dump-autoload --optimize --classmap-authoritative
```

### 4. Permission Caching Strategy

```php
// app/Http/Middleware/CacheUserPermissions.php
class CacheUserPermissions
{
    public function handle($request, Closure $next)
    {
        if (auth()->check()) {
            $userId = auth()->id();
            $cacheKey = "user.{$userId}.permissions";
            
            if (!Cache::has($cacheKey)) {
                $permissions = auth()->user()->getAllPermissions();
                Cache::put($cacheKey, $permissions, 3600); // 1 hour
            }
        }
        
        return $next($request);
    }
}
```

## Security Considerations

### 1. Permission Validation

```php
// app/Http/Middleware/ValidatePermissions.php
class ValidatePermissions
{
    public function handle($request, Closure $next, ...$permissions)
    {
        if (!auth()->check()) {
            abort(401, 'Unauthenticated');
        }

        // Log permission checks untuk audit
        Log::info('Permission check', [
            'user_id' => auth()->id(),
            'permissions' => $permissions,
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
        ]);

        if (!auth()->user()->hasAnyPermission($permissions)) {
            abort(403, 'Insufficient permissions');
        }

        return $next($request);
    }
}
```

### 2. Rate Limiting

```php
// config/permission.php - Custom rate limiting
'rate_limiting' => [
    'permission_checks' => [
        'max_attempts' => 1000,
        'decay_minutes' => 1,
    ],
    'role_assignments' => [
        'max_attempts' => 10,
        'decay_minutes' => 60,
    ],
],
```

### 3. Audit Logging

```php
// app/Models/PermissionAudit.php
class PermissionAudit extends Model
{
    protected $fillable = [
        'user_id',
        'action',
        'permission',
        'result',
        'ip_address',
        'user_agent',
    ];

    public static function log($action, $permission, $result)
    {
        static::create([
            'user_id' => auth()->id(),
            'action' => $action,
            'permission' => $permission,
            'result' => $result,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
        ]);
    }
}
```

## Monitoring & Maintenance

### 1. Health Checks

```php
// app/Http/Controllers/HealthController.php
class HealthController extends Controller
{
    public function rajashield()
    {
        $checks = [
            'module_enabled' => Module::find('RajaShield')?->isEnabled(),
            'permissions_count' => Permission::count(),
            'roles_count' => Role::count(),
            'super_admin_exists' => Role::where('name', 'super-admin')->exists(),
            'cache_working' => Cache::store('default')->get('test') !== null,
        ];

        $healthy = collect($checks)->every(fn($check) => $check === true);

        return response()->json([
            'status' => $healthy ? 'healthy' : 'unhealthy',
            'checks' => $checks,
            'timestamp' => now(),
        ], $healthy ? 200 : 500);
    }
}
```

### 2. Monitoring Commands

```bash
# Cek status permissions
php artisan rajashield:sync-permissions --dry-run

# Cek cache permissions
php artisan tinker
# Cache::get('spatie.permission.cache')

# Monitor database size
SELECT 
    table_name,
    table_rows,
    ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'Size (MB)'
FROM information_schema.tables 
WHERE table_schema = 'your_database'
AND table_name IN ('roles', 'permissions', 'role_has_permissions', 'model_has_roles');
```

### 3. Automated Maintenance

```bash
#!/bin/bash
# maintenance-rajashield.sh

echo "🔧 Starting RajaShield maintenance..."

# Clear old permission cache
php artisan permission:cache-reset

# Sync permissions (check for new entities)
php artisan rajashield:sync-permissions --dry-run

# Clean up orphaned permissions (optional)
# php artisan rajashield:cleanup-permissions

# Optimize database
php artisan db:optimize

echo "✅ RajaShield maintenance completed!"
```

### 4. Backup Strategy

```bash
# Daily backup script
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)

# Backup permission tables
mysqldump -u username -p database_name \
  roles permissions role_has_permissions model_has_roles model_has_permissions \
  > rajashield_backup_$DATE.sql

# Compress backup
gzip rajashield_backup_$DATE.sql

# Keep only last 30 days
find /backup/path -name "rajashield_backup_*.sql.gz" -mtime +30 -delete
```

## Rollback Strategy

### 1. Permission Rollback

```bash
# Backup current state
php artisan rajashield:export-permissions > permissions_backup.json

# Rollback to previous state
php artisan rajashield:import-permissions < permissions_previous.json
```

### 2. Database Rollback

```bash
# Rollback migrations
php artisan migrate:rollback --path=Modules/RajaShield/database/migrations

# Restore from backup
mysql -u username -p database_name < backup_file.sql
```

### 3. Module Rollback

```bash
# Disable module
php artisan module:disable RajaShield

# Clear cache
php artisan cache:clear
php artisan config:clear
```

## Production Checklist

### ✅ Pre-deployment
- [ ] Database backup completed
- [ ] Dependencies verified
- [ ] Configuration validated
- [ ] Test environment validated

### ✅ Deployment
- [ ] Module enabled successfully
- [ ] Migrations completed
- [ ] Seeders executed
- [ ] Permissions synced
- [ ] Cache optimized

### ✅ Post-deployment
- [ ] Health checks passed
- [ ] Permission tests passed
- [ ] Performance metrics normal
- [ ] Monitoring alerts configured
- [ ] Backup strategy implemented

### ✅ Verification
- [ ] Admin panel accessible
- [ ] Role management working
- [ ] Permission checks working
- [ ] Sync functionality working
- [ ] No error logs

**Deployment RajaShield ke production sekarang sudah selesai! 🚀**
