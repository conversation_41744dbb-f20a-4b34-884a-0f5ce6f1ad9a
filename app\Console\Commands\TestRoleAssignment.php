<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use Spatie\Permission\Models\Role;

class TestRoleAssignment extends Command
{
    protected $signature = 'test:role-assignment';
    protected $description = 'Test role assignment functionality';

    public function handle()
    {
        $this->info("=== TESTING ROLE ASSIGNMENT ===");
        
        // Test konversi ID ke nama role
        $this->info("\n1. Testing ID to Role Name conversion:");
        
        $roleIds = [29, 31]; // ID super-admin dan user
        $roles = Role::whereIn('id', $roleIds)->get();
        
        $this->info("Available roles:");
        Role::all()->each(function($role) {
            $this->info("- ID: {$role->id}, Name: {$role->name}");
        });
        
        if ($roles->count() > 0) {
            $this->info("\nRole IDs {" . implode(', ', $roleIds) . "} converted to:");
            $roleNames = $roles->pluck('name')->toArray();
            foreach ($roleNames as $name) {
                $this->info("- {$name}");
            }
            
            // Test assignment ke user
            $user = User::first();
            if ($user) {
                $this->info("\nTesting assignment to user: {$user->name}");
                
                try {
                    $user->syncRoles($roleNames);
                    $this->info("✓ Role assignment successful");
                    
                    $currentRoles = $user->fresh()->roles->pluck('name')->toArray();
                    $this->info("Current user roles: " . implode(', ', $currentRoles));
                    
                } catch (\Exception $e) {
                    $this->error("✗ Role assignment failed: " . $e->getMessage());
                }
            }
        } else {
            $this->warn("No roles found with IDs: " . implode(', ', $roleIds));
        }
        
        return 0;
    }
}
