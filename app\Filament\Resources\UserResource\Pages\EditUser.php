<?php

namespace App\Filament\Resources\UserResource\Pages;

use App\Filament\Resources\UserResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Spatie\Permission\Models\Role;

class EditUser extends EditRecord
{
    protected static string $resource = UserResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        return $data;
    }

    protected function afterSave(): void
    {
        // Pastikan relasi roles disimpan dengan benar
        $record = $this->getRecord();

        if ($record && isset($this->data['roles']) && !empty($this->data['roles'])) {
            // Konversi role IDs ke role names
            $roleNames = Role::whereIn('id', $this->data['roles'])->pluck('name')->toArray();

            // Sync roles menggunakan nama role
            $record->syncRoles($roleNames);

            // Refresh model untuk memuat relasi yang baru
            $record->refresh();
        }
    }
}
