# Troubleshooting RajaShield

## Daftar Isi

1. [<PERSON><PERSON><PERSON>](#masalah-instalasi)
2. [Ma<PERSON>ah Permission](#masalah-permission)
3. [<PERSON><PERSON><PERSON> Sync](#masalah-sync)
4. [Masalah Performance](#masalah-performance)
5. [<PERSON><PERSON><PERSON> FilamentPHP](#masalah-filamentphp)
6. [Error Messages](#error-messages)

## Masalah Instalasi

### Error: Class 'Modules\RajaShield\Models\Role' not found

**Penyebab**: Autoloader belum di-refresh atau modul belum di-enable.

**Solusi**:
```bash
# Refresh autoloader
composer dump-autoload

# Enable modul
php artisan module:enable RajaShield

# Clear cache
php artisan config:clear
php artisan cache:clear
```

### Error: Table 'roles' doesn't exist

**Penyebab**: Migrasi Spatie Permission belum dijalankan.

**Solusi**:
```bash
# Publish dan jalankan migrasi Spatie Permission
php artisan vendor:publish --provider="Spatie\Permission\PermissionServiceProvider"
php artisan migrate

# Jalankan migrasi RajaShield
php artisan module:migrate RajaShield
```

### Error: SQLSTATE[42S01]: Base table or view already exists

**Penyebab**: Tabel permission sudah ada dari instalasi sebelumnya.

**Solusi**:
```bash
# Cek status migrasi
php artisan migrate:status

# Jika perlu, rollback dan migrate ulang
php artisan migrate:rollback --step=5
php artisan migrate
```

### Error: Class 'Spatie\Permission\Traits\HasRoles' not found

**Penyebab**: Package Spatie Permission belum terinstall.

**Solusi**:
```bash
# Install package
composer require spatie/laravel-permission

# Publish konfigurasi
php artisan vendor:publish --provider="Spatie\Permission\PermissionServiceProvider"
```

## Masalah Permission

### User tidak bisa akses meskipun sudah ada permission

**Penyebab**: Cache permission belum di-clear atau role tidak ter-assign dengan benar.

**Solusi**:
```bash
# Clear permission cache
php artisan permission:cache-reset

# Atau clear semua cache
php artisan cache:clear
```

**Cek di code**:
```php
// Cek role user
$user = User::find(1);
dd($user->roles); // Harus ada role

// Cek permission user
dd($user->permissions); // Harus ada permission

// Cek permission melalui role
dd($user->getPermissionsViaRoles());
```

### Super admin tidak bisa akses semua fitur

**Penyebab**: Logic super admin tidak diimplementasi dengan benar.

**Solusi**:
```php
// Di Resource/Page/Widget
public static function canViewAny(): bool
{
    // Super admin bypass
    if (auth()->user()->hasRole('super-admin')) {
        return true;
    }
    
    return auth()->user()->can('view_any_posts');
}
```

### Permission tidak muncul di form role

**Penyebab**: Permission belum di-sync atau tidak terdeteksi.

**Solusi**:
```bash
# Sync permissions
php artisan rajashield:sync-permissions --force

# Cek apakah Resource/Page/Widget dapat di-load
php artisan tinker
```

```php
// Di tinker
use Filament\Facades\Filament;
dd(Filament::getResources()); // Harus menampilkan resources
```

### Error: This action is unauthorized

**Penyebab**: User tidak memiliki permission yang diperlukan.

**Solusi**:
```php
// Cek permission user
$user = auth()->user();
dd($user->can('required_permission'));

// Assign permission jika perlu
$user->givePermissionTo('required_permission');

// Atau assign role
$user->assignRole('admin');
```

## Masalah Sync

### Sync permissions tidak mendeteksi Resource baru

**Penyebab**: Resource tidak menggunakan namespace yang benar atau ada error di class.

**Solusi**:
```bash
# Clear compiled files
php artisan clear-compiled
php artisan config:clear

# Cek apakah Resource dapat di-load
php artisan tinker
```

```php
// Di tinker, cek Resource
$resource = 'App\\Filament\\Resources\\NewResource';
class_exists($resource); // Harus true

// Cek apakah Resource terdaftar di Filament
use Filament\Facades\Filament;
in_array($resource, Filament::getResources()); // Harus true
```

### Sync menghapus permission yang masih digunakan

**Penyebab**: Resource/Page/Widget sudah dihapus tapi permission masih ada.

**Solusi**:
```bash
# Dry run untuk preview
php artisan rajashield:sync-permissions --dry-run

# Jika yakin, jalankan sync
php artisan rajashield:sync-permissions --force
```

### Error saat sync: Class not found

**Penyebab**: Ada Resource/Page/Widget yang tidak dapat di-load.

**Solusi**:
```bash
# Cek error log
tail -f storage/logs/laravel.log

# Clear cache dan autoloader
php artisan config:clear
php artisan cache:clear
composer dump-autoload
```

## Masalah Performance

### Query permission lambat

**Penyebab**: Terlalu banyak query permission atau cache tidak optimal.

**Solusi**:
```php
// Eager load permissions
$users = User::with('roles.permissions')->get();

// Cache permissions
$permissions = Cache::remember(
    "user.{$userId}.permissions",
    3600,
    fn() => $user->getAllPermissions()
);
```

### Memory limit exceeded saat sync

**Penyebab**: Terlalu banyak entities atau memory tidak cukup.

**Solusi**:
```bash
# Increase memory limit
php -d memory_limit=512M artisan rajashield:sync-permissions

# Atau edit php.ini
memory_limit = 512M
```

### Slow query pada tabel permissions

**Penyebab**: Index database tidak optimal.

**Solusi**:
```sql
-- Tambah index untuk performa
CREATE INDEX idx_permissions_name ON permissions(name);
CREATE INDEX idx_role_has_permissions_role_id ON role_has_permissions(role_id);
CREATE INDEX idx_model_has_roles_model ON model_has_roles(model_type, model_id);
```

## Masalah FilamentPHP

### Menu RajaShield tidak muncul

**Penyebab**: Resources tidak terdaftar di panel atau user tidak memiliki akses.

**Solusi**:
```php
// Cek di PanelProvider
public function panel(Panel $panel): Panel
{
    return $panel
        ->resources([
            \Modules\RajaShield\Filament\Resources\RoleResource::class,
            \Modules\RajaShield\Filament\Resources\PermissionResource::class,
        ])
        ->pages([
            \Modules\RajaShield\Filament\Pages\SyncPermissions::class,
        ]);
}
```

### Error: Target class does not exist

**Penyebab**: Class Resource/Page tidak ditemukan.

**Solusi**:
```bash
# Cek namespace dan path file
# Pastikan file ada di lokasi yang benar
ls -la Modules/RajaShield/app/Filament/Resources/

# Clear cache
php artisan config:clear
composer dump-autoload
```

### Form permission tidak tersimpan

**Penyebab**: Validation error atau mass assignment issue.

**Solusi**:
```php
// Cek di model Role
protected $fillable = [
    'name',
    'guard_name',
    'description',
];

// Cek validation rules
public static function form(Form $form): Form
{
    return $form->schema([
        TextInput::make('name')
            ->required()
            ->unique(ignoreRecord: true), // Penting untuk edit
    ]);
}
```

## Error Messages

### "This action is unauthorized"

**Penyebab**: User tidak memiliki permission.

**Debug**:
```php
// Cek permission yang diperlukan
$user = auth()->user();
$requiredPermission = 'view_any_posts';

if (!$user->can($requiredPermission)) {
    // User tidak memiliki permission
    dd($user->getAllPermissions()); // Lihat semua permission user
}
```

**Solusi**:
```php
// Assign permission
$user->givePermissionTo($requiredPermission);

// Atau assign role yang memiliki permission
$user->assignRole('admin');
```

### "Role does not exist"

**Penyebab**: Role belum dibuat atau nama salah.

**Debug**:
```php
// Cek role yang ada
use Modules\RajaShield\Models\Role;
dd(Role::all()->pluck('name'));
```

**Solusi**:
```php
// Buat role jika belum ada
Role::create([
    'name' => 'admin',
    'description' => 'Administrator',
]);
```

### "Permission does not exist"

**Penyebab**: Permission belum dibuat atau nama salah.

**Debug**:
```php
// Cek permission yang ada
use Modules\RajaShield\Models\Permission;
dd(Permission::all()->pluck('name'));
```

**Solusi**:
```bash
# Sync permissions
php artisan rajashield:sync-permissions --force
```

### "SQLSTATE[23000]: Integrity constraint violation"

**Penyebab**: Duplicate entry atau foreign key constraint.

**Debug**:
```bash
# Cek error detail di log
tail -f storage/logs/laravel.log
```

**Solusi**:
```php
// Gunakan firstOrCreate untuk avoid duplicate
Role::firstOrCreate([
    'name' => 'admin',
], [
    'description' => 'Administrator',
]);
```

### "Class 'Filament\Facades\Filament' not found"

**Penyebab**: FilamentPHP belum terinstall atau versi tidak kompatibel.

**Solusi**:
```bash
# Install FilamentPHP
composer require filament/filament:"^3.2"

# Publish assets
php artisan filament:install --panels
```

## Debug Tools

### Cek Status Modul

```bash
# List semua modul
php artisan module:list

# Cek detail modul
php artisan module:show RajaShield
```

### Cek Permission User

```php
// Di tinker atau controller
$user = User::find(1);

// Semua roles
dd($user->roles->pluck('name'));

// Semua permissions
dd($user->getAllPermissions()->pluck('name'));

// Permission melalui roles
dd($user->getPermissionsViaRoles()->pluck('name'));

// Direct permissions
dd($user->getDirectPermissions()->pluck('name'));
```

### Cek FilamentPHP Entities

```php
// Di tinker
use Filament\Facades\Filament;

// Semua resources
dd(Filament::getResources());

// Semua pages
dd(Filament::getPages());

// Semua widgets
dd(Filament::getWidgets());
```

### Log Permission Checks

```php
// Tambah logging di middleware atau controller
Log::info('Permission check', [
    'user_id' => auth()->id(),
    'permission' => $permission,
    'has_permission' => auth()->user()->can($permission),
    'roles' => auth()->user()->roles->pluck('name'),
]);
```

Lanjutkan ke [FAQ](faq.md) untuk pertanyaan yang sering diajukan.
