<?php

namespace Modules\RajaMember\Filament\rajamember\Pages\Auth;

use Filament\Pages\Auth\Register as BaseRegister;
use Filament\Forms\Components\Component;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Checkbox;
use Filament\Notifications\Notification;
use Filament\Http\Responses\Auth\Contracts\RegistrationResponse;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;
use App\Models\User;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Auth\Events\Registered;

class Register extends BaseRegister
{
    public function getTitle(): string|Htmlable
    {
        return 'Pendaftaran Member';
    }

    public function getHeading(): string|Htmlable
    {
        return 'Daftar Member Baru';
    }

    public function getSubheading(): string|Htmlable
    {
        return 'Masukkan nama dan email Anda. Data login akan dikirim ke email.';
    }

    protected function getForms(): array
    {
        return [
            'form' => $this->form(
                $this->makeForm()
                    ->schema([
                        $this->getNameFormComponent(),
                        $this->getEmailFormComponent(),
                        $this->getAgreementFormComponent(),
                    ])
                    ->statePath('data'),
            ),
        ];
    }

    protected function getNameFormComponent(): Component
    {
        return TextInput::make('name')
            ->label('Nama Lengkap')
            ->required()
            ->maxLength(255)
            ->placeholder('Masukkan nama lengkap Anda')
            ->autofocus();
    }

    protected function getEmailFormComponent(): Component
    {
        return TextInput::make('email')
            ->label('Alamat Email')
            ->email()
            ->required()
            ->unique(User::class)
            ->maxLength(255)
            ->placeholder('<EMAIL>')
            ->helperText('Data login akan dikirim ke email ini');
    }

    protected function getAgreementFormComponent(): Component
    {
        return Checkbox::make('setuju_syarat')
            ->label('Saya setuju dengan syarat dan ketentuan')
            ->required()
            ->accepted();
    }

    public function register(): ?RegistrationResponse
    {
        $data = $this->form->getState();

        try {
            // Hapus setuju_syarat dari data
            unset($data['setuju_syarat']);

            // Generate password otomatis (8 karakter random)
            $generatedPassword = $this->generateRandomPassword();
            $data['password'] = Hash::make($generatedPassword);

            // Buat member baru
            $member = User::create($data);
            // Role 'member' sudah otomatis di-assign melalui User model boot event

            // Fire registered event
            event(new Registered($member));

            // Kirim email dengan data login
            $this->sendWelcomeEmail($member, $generatedPassword);

            Notification::make()
                ->title('Pendaftaran Berhasil!')
                ->body("Selamat datang {$member->name}! Data login telah dikirim ke email Anda.")
                ->success()
                ->duration(8000)
                ->send();

            // Return registration response
            return app(RegistrationResponse::class);

        } catch (\Exception $e) {
            Log::error('Member registration failed', [
                'error' => $e->getMessage(),
                'data' => $data
            ]);

            Notification::make()
                ->title('Pendaftaran Gagal')
                ->body('Terjadi kesalahan saat mendaftar. Silakan coba lagi.')
                ->danger()
                ->send();

            return null;
        }
    }

    /**
     * Generate random password
     */
    private function generateRandomPassword(int $length = 8): string
    {
        $characters = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        $password = '';

        for ($i = 0; $i < $length; $i++) {
            $password .= $characters[rand(0, strlen($characters) - 1)];
        }

        return $password;
    }

    /**
     * Send welcome email with login credentials
     */
    private function sendWelcomeEmail(User $member, string $password): void
    {
        try {
            // Kirim email menggunakan Mail facade
            Mail::send('rajamember::emails.welcome', [
                'member' => $member,
                'password' => $password,
                'loginUrl' => url('/member/login')
            ], function ($message) use ($member) {
                $message->to($member->email, $member->name)
                        ->subject('Selamat Datang - Data Login Member Anda');
            });

        } catch (\Exception $e) {
            // Log error jika email gagal dikirim
            Log::error('Failed to send welcome email to member: ' . $member->email, [
                'error' => $e->getMessage(),
                'member_id' => $member->id
            ]);
        }
    }
}
