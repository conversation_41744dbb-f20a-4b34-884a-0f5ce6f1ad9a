<?php

namespace Modules\RajaMember\Providers;

use App\Models\User;
use DutchCodingCompany\FilamentDeveloperLogins\FilamentDeveloperLoginsPlugin;
use Filament\Http\Middleware\Authenticate;
use Filament\Http\Middleware\DisableBladeIconComponents;
use Filament\Http\Middleware\DispatchServingFilamentEvent;
use Filament\Navigation\NavigationGroup;
use Filament\Navigation\NavigationItem;
use Filament\Panel;
use Filament\PanelProvider;
use Filament\Support\Colors\Color;
use Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse;
use Illuminate\Cookie\Middleware\EncryptCookies;
use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken;
use Illuminate\Routing\Middleware\SubstituteBindings;
use Illuminate\Session\Middleware\AuthenticateSession;
use Illuminate\Session\Middleware\StartSession;
use Illuminate\View\Middleware\ShareErrorsFromSession;
use Jeffgreco13\FilamentBreezy\BreezyCore;
use Modules\RajaMember\Filament\rajamember\Pages\Dashboard;
use Modules\RajaMember\Filament\rajamember\Pages\PendaftaranMember;
use Modules\RajaMember\Filament\rajamember\Pages\TestPermission;
use Modules\RajaMember\Filament\rajamember\Pages\Auth\Login;
use Modules\RajaMember\Filament\rajamember\Pages\Auth\Register;
use Modules\RajaMember\Filament\rajamember\Widgets\MemberStatsWidget;
use Illuminate\Support\Facades\Auth;

class RajaMemberPanelProvider extends PanelProvider
{
    public function panel(Panel $panel): Panel
    {
        return $panel
            ->id('rajamember')
            ->path('member')
            ->login(Login::class)
            // ->registration(Register::class)
            ->passwordReset()
            ->emailVerification()
            ->profile()
            ->brandName('Raja Member')
            ->topNavigation()
            ->colors([
                'primary' => Color::Blue,
                'danger' => Color::Rose,
                'success' => Color::Emerald,
                'warning' => Color::Orange,
            ])
            ->globalSearch(false)
            ->viteTheme('resources/css/filament/admin/theme.css')
            ->navigationGroups([
                NavigationGroup::make('Member'),
                NavigationGroup::make('Profil'),
                NavigationGroup::make('Pengaturan'),
            ])

            ->pages([
                Dashboard::class,
                PendaftaranMember::class,
                TestPermission::class,
            ])
            ->resources([
                \Modules\RajaMember\Filament\Resources\MemberResource::class,
            ])
            ->widgets([
                MemberStatsWidget::class,
            ])
            ->discoverResources(in: __DIR__ . '/../Filament/Resources', for: 'Modules\\RajaMember\\Filament\\Resources')
            ->discoverPages(in: __DIR__ . '/../Filament/Pages', for: 'Modules\\RajaMember\\Filament\\Pages')
            ->discoverWidgets(in: __DIR__ . '/../Filament/Widgets', for: 'Modules\\RajaMember\\Filament\\Widgets')
            ->middleware([
                EncryptCookies::class,
                AddQueuedCookiesToResponse::class,
                StartSession::class,
                AuthenticateSession::class,
                ShareErrorsFromSession::class,
                VerifyCsrfToken::class,
                SubstituteBindings::class,
                DisableBladeIconComponents::class,
                DispatchServingFilamentEvent::class,
            ])
            ->authMiddleware([
                Authenticate::class,
            ])
            ->plugins([
                FilamentDeveloperLoginsPlugin::make()
                    ->enabled()
                    ->users(fn() => User::pluck('email', 'name')->toArray()),
                \TomatoPHP\FilamentPlugins\FilamentPluginsPlugin::make()->discoverCurrentPanelOnly()->useUI(false),
              
                BreezyCore::make()
                    ->myProfile(
                        shouldRegisterUserMenu: true, // Sets the 'account' link in the panel User Menu (default = true)
                        userMenuLabel: 'Profileku', // Customizes the 'account' link label in the panel User Menu (default = null)
                        shouldRegisterNavigation: false, // Adds a main navigation item for the My Profile page (default = false)
                        navigationGroup: 'Pengaturan', // Sets the navigation group for the My Profile page (default = null)
                        hasAvatars: false, // Enables the avatar upload form component (default = false)
                        slug: 'my-profile' // Sets the slug for the profile page (default = 'my-profile')
                    ),

            ]);
    }

    /**
     * Method untuk cek akses permission
     */
    protected function cekAkses($permission)
    {
        return fn() => Auth::check() && (Auth::user()->hasRole('super-admin') || Auth::user()->can($permission));
    }
}
