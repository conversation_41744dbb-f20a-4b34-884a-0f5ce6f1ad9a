<?php

namespace Modules\RajaMember\Filament\rajamember\Pages;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Pages\Page;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use App\Models\User;


class PendaftaranMember extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-user-plus';
    protected static string $view = 'rajamember::filament.pages.pendaftaran-member';
    protected static ?string $title = 'Pendaftaran Member';
    protected static ?string $navigationLabel = 'Daftar Member';
    protected static ?string $navigationGroup = 'Member';
    protected static ?int $navigationSort = 1;

    // Izinkan akses tanpa autentikasi
    protected static bool $shouldRegisterNavigation = false;

    public ?array $data = [];

    public function mount(): void
    {
        $this->form->fill();
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Pendaftaran Member')
                    ->description('Masukkan nama dan email Anda. Data login akan dikirim ke email.')
                    ->icon('heroicon-o-user-plus')
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->label('Nama Lengkap')
                            ->required()
                            ->maxLength(255)
                            ->placeholder('Masukkan nama lengkap Anda')
                            ->autofocus(),

                        Forms\Components\TextInput::make('email')
                            ->label('Alamat Email')
                            ->email()
                            ->required()
                            ->unique(User::class)
                            ->maxLength(255)
                            ->placeholder('<EMAIL>')
                            ->helperText('Data login akan dikirim ke email ini'),

                        Forms\Components\Checkbox::make('setuju_syarat')
                            ->label('Saya setuju dengan syarat dan ketentuan')
                            ->required()
                            ->accepted(),
                    ])
                    ->columns(1)
                    ->compact(),
            ])
            ->statePath('data');
    }



    public function daftarMember(): void
    {
        $data = $this->form->getState();

        try {
            // Hapus setuju_syarat dari data
            unset($data['setuju_syarat']);

            // Generate password otomatis (8 karakter random)
            $generatedPassword = $this->generateRandomPassword();
            $data['password'] = Hash::make($generatedPassword);

            // Buat member baru
            $member = User::create($data);
            // Role 'member' sudah otomatis di-assign melalui User model boot event

            // Kirim email dengan data login
            $this->sendWelcomeEmail($member, $generatedPassword);

            Notification::make()
                ->title('Pendaftaran Berhasil!')
                ->body("Selamat datang {$member->name}! Data login telah dikirim ke email Anda.")
                ->success()
                ->duration(8000)
                ->send();

            // Reset form
            $this->form->fill();

            // Redirect ke halaman login atau dashboard
            $this->redirect('/member/login');

        } catch (\Exception $e) {
            Log::error('Member registration failed', [
                'error' => $e->getMessage(),
                'data' => $data
            ]);

            Notification::make()
                ->title('Pendaftaran Gagal')
                ->body('Terjadi kesalahan saat mendaftar. Silakan coba lagi.')
                ->danger()
                ->send();
        }
    }

    /**
     * Generate random password
     */
    private function generateRandomPassword(int $length = 8): string
    {
        $characters = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        $password = '';

        for ($i = 0; $i < $length; $i++) {
            $password .= $characters[rand(0, strlen($characters) - 1)];
        }

        return $password;
    }

    /**
     * Send welcome email with login credentials
     */
    private function sendWelcomeEmail(User $member, string $password): void
    {
        try {
            // Kirim email menggunakan Mail facade
            Mail::send('rajamember::emails.welcome', [
                'member' => $member,
                'password' => $password,
                'loginUrl' => url('/member/login')
            ], function ($message) use ($member) {
                $message->to($member->email, $member->name)
                        ->subject('Selamat Datang - Data Login Member Anda');
            });

        } catch (\Exception $e) {
            // Log error jika email gagal dikirim
            Log::error('Failed to send welcome email to member: ' . $member->email, [
                'error' => $e->getMessage(),
                'member_id' => $member->id
            ]);
        }
    }

    // ========== PERMISSION CHECKING ==========

    public static function canAccess(): bool
    {
        return Auth::check() && (Auth::user()->hasRole('super-admin') || Auth::user()->can('view_pendaftaran_member'));
    }
}
