# API Reference RajaShield

## Daftar Isi

1. [Models](#models)
2. [Helper Classes](#helper-classes)
3. [Commands](#commands)
4. [Middleware](#middleware)
5. [Blade Directives](#blade-directives)
6. [FilamentPHP Integration](#filamentphp-integration)

## Models

### Role Model

```php
<?php

namespace Modules\RajaShield\Models;

use Spatie\Permission\Models\Role as SpatieRole;

class Role extends SpatieRole
{
    protected $fillable = [
        'name',
        'guard_name', 
        'description',
    ];
}
```

#### Methods

##### `create(array $attributes)`
Membuat role baru.

```php
$role = Role::create([
    'name' => 'editor',
    'description' => 'Content Editor',
]);
```

##### `givePermissionTo(...$permissions)`
Memberikan permission ke role.

```php
$role->givePermissionTo('edit_posts', 'delete_posts');
$role->givePermissionTo(['view_posts', 'create_posts']);
```

##### `revokePermissionTo(...$permissions)`
Mencabut permission dari role.

```php
$role->revokePermissionTo('delete_posts');
```

##### `syncPermissions(...$permissions)`
Sinkronisasi permissions (replace semua).

```php
$role->syncPermissions(['view_posts', 'edit_posts']);
```

### Permission Model

```php
<?php

namespace Modules\RajaShield\Models;

use Spatie\Permission\Models\Permission as SpatiePermission;

class Permission extends SpatiePermission
{
    protected $fillable = [
        'name',
        'guard_name',
        'description',
        'group',
    ];
}
```

#### Methods

##### `create(array $attributes)`
Membuat permission baru.

```php
$permission = Permission::create([
    'name' => 'edit_posts',
    'description' => 'Edit blog posts',
    'group' => 'content',
]);
```

##### `assignRole(...$roles)`
Assign permission ke role.

```php
$permission->assignRole('editor', 'admin');
```

## Helper Classes

### RajaShieldPermissionManager

```php
<?php

namespace Modules\RajaShield\Helpers;

class RajaShieldPermissionManager
{
    // Constants
    const ENTITY_TYPE_RESOURCE = 'resource';
    const ENTITY_TYPE_PAGE = 'page';
    const ENTITY_TYPE_WIDGET = 'widget';
    
    const RESOURCE_PERMISSION_ACTIONS = [
        'view', 'view_any', 'create', 'update', 'delete', 'delete_any'
    ];
}
```

#### Static Methods

##### `getResources(): Collection`
Mendapatkan semua FilamentPHP Resources.

```php
$resources = RajaShieldPermissionManager::getResources();
```

##### `getPages(): Collection`
Mendapatkan semua FilamentPHP Pages.

```php
$pages = RajaShieldPermissionManager::getPages();
```

##### `getWidgets(): Collection`
Mendapatkan semua FilamentPHP Widgets.

```php
$widgets = RajaShieldPermissionManager::getWidgets();
```

##### `generateAllPermissions(): array`
Generate semua permissions untuk entities.

```php
$permissions = RajaShieldPermissionManager::generateAllPermissions();
```

##### `getPermissionsGroupedByEntity(): array`
Mendapatkan permissions yang dikelompokkan berdasarkan entity.

```php
$grouped = RajaShieldPermissionManager::getPermissionsGroupedByEntity();
```

##### `generateResourcePermissionName(string $resource, string $action): string`
Generate nama permission untuk resource.

```php
$permissionName = RajaShieldPermissionManager::generateResourcePermissionName(
    'App\\Filament\\Resources\\PostResource',
    'view_any'
); // Result: 'view_any_posts'
```

##### `generatePagePermissionName(string $page): string`
Generate nama permission untuk page.

```php
$permissionName = RajaShieldPermissionManager::generatePagePermissionName(
    'App\\Filament\\Pages\\Analytics'
); // Result: 'view_page_analytics'
```

##### `generateWidgetPermissionName(string $widget): string`
Generate nama permission untuk widget.

```php
$permissionName = RajaShieldPermissionManager::generateWidgetPermissionName(
    'App\\Filament\\Widgets\\StatsOverview'
); // Result: 'view_widget_stats_overview'
```

## Commands

### SyncPermissionsCommand

```bash
php artisan rajashield:sync-permissions [options]
```

#### Options

- `--force` : Force sync tanpa konfirmasi
- `--dry-run` : Preview sync tanpa melakukan perubahan

#### Examples

```bash
# Preview sync
php artisan rajashield:sync-permissions --dry-run

# Sync dengan konfirmasi
php artisan rajashield:sync-permissions

# Force sync
php artisan rajashield:sync-permissions --force
```

## Middleware

### Built-in Spatie Middleware

#### RoleMiddleware

```php
// Di routes
Route::middleware(['role:admin'])->group(function () {
    // Routes untuk admin
});

Route::middleware(['role:admin|editor'])->group(function () {
    // Routes untuk admin atau editor
});
```

#### PermissionMiddleware

```php
// Di routes
Route::middleware(['permission:edit_posts'])->group(function () {
    // Routes untuk user dengan permission edit_posts
});

Route::middleware(['permission:edit_posts|delete_posts'])->group(function () {
    // Routes untuk user dengan permission edit_posts atau delete_posts
});
```

#### RoleOrPermissionMiddleware

```php
// Di routes
Route::middleware(['role_or_permission:admin|edit_posts'])->group(function () {
    // Routes untuk admin ATAU user dengan permission edit_posts
});
```

### Custom Middleware

Daftarkan di `app/Http/Kernel.php`:

```php
protected $middlewareAliases = [
    'role' => \Spatie\Permission\Middlewares\RoleMiddleware::class,
    'permission' => \Spatie\Permission\Middlewares\PermissionMiddleware::class,
    'role_or_permission' => \Spatie\Permission\Middlewares\RoleOrPermissionMiddleware::class,
];
```

## Blade Directives

### Role Directives

#### `@role('role_name')`
Cek apakah user memiliki role tertentu.

```blade
@role('admin')
    <p>Anda adalah admin</p>
@endrole

@role('admin|editor')
    <p>Anda adalah admin atau editor</p>
@endrole
```

#### `@hasrole('role_name')`
Alias untuk `@role`.

```blade
@hasrole('admin')
    <p>Anda adalah admin</p>
@endhasrole
```

#### `@hasanyrole('role1|role2')`
Cek apakah user memiliki salah satu dari beberapa role.

```blade
@hasanyrole('admin|editor|moderator')
    <p>Anda memiliki akses khusus</p>
@endhasanyrole
```

#### `@hasallroles('role1|role2')`
Cek apakah user memiliki semua role yang disebutkan.

```blade
@hasallroles('admin|editor')
    <p>Anda adalah admin dan editor</p>
@endhasallroles
```

### Permission Directives

#### `@can('permission_name')`
Cek permission menggunakan Laravel Gate.

```blade
@can('edit_posts')
    <a href="{{ route('posts.edit', $post) }}">Edit</a>
@endcan

@can('update', $post)
    <a href="{{ route('posts.edit', $post) }}">Edit Post</a>
@endcan
```

#### `@cannot('permission_name')`
Kebalikan dari `@can`.

```blade
@cannot('delete_posts')
    <p>Anda tidak dapat menghapus post</p>
@endcannot
```

### Custom Directives

#### `@superadmin`
Cek apakah user adalah super admin.

```blade
@superadmin
    <div class="admin-panel">
        <p>Panel Super Admin</p>
    </div>
@endsuperadmin
```

## FilamentPHP Integration

### Resource Permission Methods

#### `canViewAny(): bool`
Cek apakah user dapat melihat daftar records.

```php
public static function canViewAny(): bool
{
    return auth()->user()->can('view_any_posts');
}
```

#### `canView(Model $record): bool`
Cek apakah user dapat melihat record tertentu.

```php
public static function canView(Model $record): bool
{
    return auth()->user()->can('view_posts');
}
```

#### `canCreate(): bool`
Cek apakah user dapat membuat record baru.

```php
public static function canCreate(): bool
{
    return auth()->user()->can('create_posts');
}
```

#### `canEdit(Model $record): bool`
Cek apakah user dapat mengedit record.

```php
public static function canEdit(Model $record): bool
{
    return auth()->user()->can('update_posts');
}
```

#### `canDelete(Model $record): bool`
Cek apakah user dapat menghapus record.

```php
public static function canDelete(Model $record): bool
{
    return auth()->user()->can('delete_posts');
}
```

#### `canDeleteAny(): bool`
Cek apakah user dapat bulk delete.

```php
public static function canDeleteAny(): bool
{
    return auth()->user()->can('delete_any_posts');
}
```

### Page Permission Methods

#### `canAccess(): bool`
Cek apakah user dapat mengakses page.

```php
public static function canAccess(): bool
{
    return auth()->user()->can('view_page_analytics');
}
```

### Widget Permission Methods

#### `canView(): bool`
Cek apakah user dapat melihat widget.

```php
public static function canView(): bool
{
    return auth()->user()->can('view_widget_stats_overview');
}
```

### Query Modification

#### `getEloquentQuery(): Builder`
Modifikasi query berdasarkan permissions.

```php
public static function getEloquentQuery(): Builder
{
    $query = parent::getEloquentQuery();
    
    // Jika bukan admin, hanya tampilkan data milik sendiri
    if (!auth()->user()->hasRole('admin')) {
        $query->where('user_id', auth()->id());
    }
    
    return $query;
}
```

### Navigation Visibility

#### `shouldRegisterNavigation(): bool`
Kontrol visibility menu navigation.

```php
public static function shouldRegisterNavigation(): bool
{
    return auth()->user()->can('view_any_posts');
}
```

Lanjutkan ke [Troubleshooting](troubleshooting.md) untuk solusi masalah umum.
