<?php

namespace App\Helpers;

use App\Models\User;
use Spatie\Permission\Models\Role;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;

class RoleHelper
{
    /**
     * Sync roles untuk user
     *
     * @param User $user
     * @param array $roleIds Array of role IDs atau role names
     * @return void
     */
    public static function syncRoles(User $user, array $roleIds): void
    {
        // Jika array kosong, hapus semua roles
        if (empty($roleIds)) {
            $user->syncRoles([]);
            return;
        }

        // Cek apakah roleIds berisi ID atau nama
        $firstElement = $roleIds[0];
        
        if (is_numeric($firstElement)) {
            // Jika berisi ID, ambil role berdasarkan ID
            $roles = Role::whereIn('id', $roleIds)->pluck('name')->toArray();
        } else {
            // Jika berisi nama, gunakan langsung
            $roles = $roleIds;
        }

        // Sync roles
        $user->syncRoles($roles);
        
        // Clear permission cache untuk user ini
        self::clearUserPermissionCache($user);
    }

    /**
     * Assign single role ke user
     *
     * @param User $user
     * @param string|int $role Role name atau ID
     * @return void
     */
    public static function assignRole(User $user, $role): void
    {
        if (is_numeric($role)) {
            $roleModel = Role::find($role);
            if ($roleModel) {
                $user->assignRole($roleModel->name);
            }
        } else {
            $user->assignRole($role);
        }
        
        self::clearUserPermissionCache($user);
    }

    /**
     * Remove role dari user
     *
     * @param User $user
     * @param string|int $role Role name atau ID
     * @return void
     */
    public static function removeRole(User $user, $role): void
    {
        if (is_numeric($role)) {
            $roleModel = Role::find($role);
            if ($roleModel) {
                $user->removeRole($roleModel->name);
            }
        } else {
            $user->removeRole($role);
        }
        
        self::clearUserPermissionCache($user);
    }

    /**
     * Cek apakah user memiliki role tertentu
     *
     * @param User $user
     * @param string|array $roles
     * @return bool
     */
    public static function hasRole(User $user, $roles): bool
    {
        return $user->hasRole($roles);
    }

    /**
     * Cek apakah user memiliki salah satu dari roles yang diberikan
     *
     * @param User $user
     * @param array $roles
     * @return bool
     */
    public static function hasAnyRole(User $user, array $roles): bool
    {
        return $user->hasAnyRole($roles);
    }

    /**
     * Cek apakah user memiliki semua roles yang diberikan
     *
     * @param User $user
     * @param array $roles
     * @return bool
     */
    public static function hasAllRoles(User $user, array $roles): bool
    {
        return $user->hasAllRoles($roles);
    }

    /**
     * Get semua roles yang tersedia
     *
     * @return Collection
     */
    public static function getAllRoles(): Collection
    {
        return Cache::remember('all_roles', 3600, function () {
            return Role::all();
        });
    }

    /**
     * Get roles untuk dropdown/select options
     *
     * @return array
     */
    public static function getRoleOptions(): array
    {
        return self::getAllRoles()->pluck('name', 'id')->toArray();
    }

    /**
     * Get role names untuk dropdown/select options
     *
     * @return array
     */
    public static function getRoleNameOptions(): array
    {
        return self::getAllRoles()->pluck('name', 'name')->toArray();
    }

    /**
     * Clear permission cache untuk user
     *
     * @param User $user
     * @return void
     */
    public static function clearUserPermissionCache(User $user): void
    {
        Cache::forget("user.{$user->id}.permissions");
        Cache::forget("user.{$user->id}.roles");
        
        // Clear spatie permission cache
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();
    }

    /**
     * Clear semua role cache
     *
     * @return void
     */
    public static function clearRoleCache(): void
    {
        Cache::forget('all_roles');
        Cache::forget('roles');
        
        // Clear spatie permission cache
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();
    }

    /**
     * Cek apakah user adalah super admin
     *
     * @param User $user
     * @return bool
     */
    public static function isSuperAdmin(User $user): bool
    {
        return $user->hasRole('super-admin');
    }

    /**
     * Get user permissions dengan cache
     *
     * @param User $user
     * @return Collection
     */
    public static function getUserPermissions(User $user): Collection
    {
        return Cache::remember(
            "user.{$user->id}.permissions",
            3600, // 1 hour
            fn() => $user->getAllPermissions()
        );
    }

    /**
     * Bulk assign role ke multiple users
     *
     * @param array $userIds
     * @param string $roleName
     * @return void
     */
    public static function bulkAssignRole(array $userIds, string $roleName): void
    {
        $users = User::whereIn('id', $userIds)->get();
        
        foreach ($users as $user) {
            $user->assignRole($roleName);
            self::clearUserPermissionCache($user);
        }
    }

    /**
     * Get users by role
     *
     * @param string $roleName
     * @return Collection
     */
    public static function getUsersByRole(string $roleName): Collection
    {
        return User::role($roleName)->get();
    }
}
