<x-filament-panels::page>
    <div class="space-y-6">
        <x-filament::section>
            <x-slot name="heading">
                Informasi User
            </x-slot>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <strong>Nama:</strong> {{ $data['user_name'] }}
                </div>
                <div>
                    <strong>Roles:</strong> 
                    @if(empty($data['user_roles']))
                        <span class="text-red-500">Tidak ada role</span>
                    @else
                        <span class="text-green-500">{{ implode(', ', $data['user_roles']) }}</span>
                    @endif
                </div>
            </div>
        </x-filament::section>

        <x-filament::section>
            <x-slot name="heading">
                Permission Check
            </x-slot>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="space-y-2">
                    <h4 class="font-semibold">Member Resource Permissions:</h4>
                    <ul class="space-y-1">
                        <li>
                            <span class="inline-flex items-center">
                                @if($data['can_view_members'])
                                    <x-heroicon-o-check-circle class="w-4 h-4 text-green-500 mr-2"/>
                                @else
                                    <x-heroicon-o-x-circle class="w-4 h-4 text-red-500 mr-2"/>
                                @endif
                                view_members
                            </span>
                        </li>
                        <li>
                            <span class="inline-flex items-center">
                                @if($data['can_view_any_members'])
                                    <x-heroicon-o-check-circle class="w-4 h-4 text-green-500 mr-2"/>
                                @else
                                    <x-heroicon-o-x-circle class="w-4 h-4 text-red-500 mr-2"/>
                                @endif
                                view_any_members
                            </span>
                        </li>
                        <li>
                            <span class="inline-flex items-center">
                                @if($data['can_create_members'])
                                    <x-heroicon-o-check-circle class="w-4 h-4 text-green-500 mr-2"/>
                                @else
                                    <x-heroicon-o-x-circle class="w-4 h-4 text-red-500 mr-2"/>
                                @endif
                                create_members
                            </span>
                        </li>
                        <li>
                            <span class="inline-flex items-center">
                                @if($data['can_update_members'])
                                    <x-heroicon-o-check-circle class="w-4 h-4 text-green-500 mr-2"/>
                                @else
                                    <x-heroicon-o-x-circle class="w-4 h-4 text-red-500 mr-2"/>
                                @endif
                                update_members
                            </span>
                        </li>
                        <li>
                            <span class="inline-flex items-center">
                                @if($data['can_delete_members'])
                                    <x-heroicon-o-check-circle class="w-4 h-4 text-green-500 mr-2"/>
                                @else
                                    <x-heroicon-o-x-circle class="w-4 h-4 text-red-500 mr-2"/>
                                @endif
                                delete_members
                            </span>
                        </li>
                    </ul>
                </div>

                <div class="space-y-2">
                    <h4 class="font-semibold">Page Permissions:</h4>
                    <ul class="space-y-1">
                        <li>
                            <span class="inline-flex items-center">
                                @if($data['can_view_dashboard_member'])
                                    <x-heroicon-o-check-circle class="w-4 h-4 text-green-500 mr-2"/>
                                @else
                                    <x-heroicon-o-x-circle class="w-4 h-4 text-red-500 mr-2"/>
                                @endif
                                view_dashboard_member
                            </span>
                        </li>
                    </ul>

                    <h4 class="font-semibold mt-4">Role Check:</h4>
                    <ul class="space-y-1">
                        <li>
                            <span class="inline-flex items-center">
                                @if($data['has_role_member'])
                                    <x-heroicon-o-check-circle class="w-4 h-4 text-green-500 mr-2"/>
                                @else
                                    <x-heroicon-o-x-circle class="w-4 h-4 text-red-500 mr-2"/>
                                @endif
                                Role: member
                            </span>
                        </li>
                        <li>
                            <span class="inline-flex items-center">
                                @if($data['has_role_superadmin'])
                                    <x-heroicon-o-check-circle class="w-4 h-4 text-green-500 mr-2"/>
                                @else
                                    <x-heroicon-o-x-circle class="w-4 h-4 text-red-500 mr-2"/>
                                @endif
                                Role: super-admin
                            </span>
                        </li>
                    </ul>
                </div>
            </div>
        </x-filament::section>

        <x-filament::section>
            <x-slot name="heading">
                Semua Permissions User
            </x-slot>

            <div class="max-h-40 overflow-y-auto">
                @if(empty($data['user_permissions']))
                    <p class="text-red-500">User tidak memiliki permission apapun</p>
                @else
                    <div class="grid grid-cols-2 md:grid-cols-3 gap-2">
                        @foreach($data['user_permissions'] as $permission)
                            <span class="text-sm bg-blue-100 text-blue-800 px-2 py-1 rounded">{{ $permission }}</span>
                        @endforeach
                    </div>
                @endif
            </div>
        </x-filament::section>
    </div>
</x-filament-panels::page>
