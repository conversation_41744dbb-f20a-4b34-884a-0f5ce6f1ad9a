<?php

namespace Modules\RajaMember\Filament\rajamember\Pages;

use Filament\Pages\Dashboard as BaseDashboard;
use Mo<PERSON>les\RajaMember\Filament\rajamember\Widgets\UserInfoWidget;
use Illuminate\Support\Facades\Auth;

class Dashboard extends BaseDashboard
{
    protected static ?string $navigationIcon = 'heroicon-o-home';
    
    protected static string $view = 'rajamember::filament.pages.dashboard';
    
    protected static ?string $title = 'Dashboard Member';
    
    protected static ?string $navigationLabel = 'Dashboard';
    
    protected static ?int $navigationSort = 1;
    // protected static string $routePath = 'member';


    
    public function getColumns(): int | string | array
    {
        return [
            'md' => 2,
            'xl' => 3,
        ];
    }
    
    public function getWidgets(): array
    {
        return [
            UserInfoWidget::class,
        ];
    }

    // ========== PERMISSION CHECKING ==========

    public static function canAccess(): bool
    {
        return Auth::check() && (Auth::user()->hasRole('super-admin') || Auth::user()->can('view_dashboard_member'));
    }
}
