<?php

namespace Modules\RajaMember\Filament\Resources;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use app\Models\User as Member;
use Modules\RajaMember\Filament\Resources\MemberResource\Pages;
use Filament\Infolists\Infolist;
use Filament\Infolists\Components;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;

class MemberResource extends Resource
{
    protected static ?string $model = Member::class;
    protected static ?string $navigationIcon = 'heroicon-o-users';
    protected static ?string $navigationLabel = 'Data Member';
    protected static ?string $modelLabel = 'Member';
    protected static ?string $pluralModelLabel = 'Member';
    protected static ?string $navigationGroup = 'Pengaturan';
    protected static ?int $navigationSort = 2;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Informasi Pribadi')
                    ->schema([
                        Forms\Components\TextInput::make('kode_member')
                            ->label('Kode Member')
                            ->disabled()
                            ->dehydrated(false),

                        Forms\Components\TextInput::make('nama')
                            ->label('Nama Lengkap')
                            ->required()
                            ->maxLength(255),

                        Forms\Components\TextInput::make('email')
                            ->label('Email')
                            ->email()
                            ->required()
                            ->unique(ignoreRecord: true)
                            ->maxLength(255),

                        Forms\Components\TextInput::make('telpon')
                            ->label('Nomor Telepon')
                            ->tel(),

                        Forms\Components\DatePicker::make('tanggal_lahir')
                            ->label('Tanggal Lahir'),

                        Forms\Components\Select::make('jenis_kelamin')
                            ->label('Jenis Kelamin')
                            ->options([
                                'L' => 'Laki-laki',
                                'P' => 'Perempuan',
                            ]),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Alamat')
                    ->schema([
                        Forms\Components\Textarea::make('alamat')
                            ->label('Alamat Lengkap')
                            ->rows(3),

                        Forms\Components\TextInput::make('kota')
                            ->label('Kota'),

                        Forms\Components\TextInput::make('provinsi')
                            ->label('Provinsi'),

                        Forms\Components\TextInput::make('kode_pos')
                            ->label('Kode Pos')
                            ->numeric(),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Informasi Tambahan')
                    ->schema([
                        Forms\Components\TextInput::make('pekerjaan')
                            ->label('Pekerjaan'),

                        Forms\Components\Select::make('status')
                            ->label('Status')
                            ->options([
                                'AKTIF' => 'Aktif',
                                'NONAKTIF' => 'Non-aktif',
                                'PENDING' => 'Pending',
                            ])
                            ->required(),

                        Forms\Components\DatePicker::make('tanggal_bergabung')
                            ->label('Tanggal Bergabung')
                            ->default(now()),

                        Forms\Components\TextInput::make('poin')
                            ->label('Poin')
                            ->numeric()
                            ->default(0),

                        Forms\Components\Select::make('roles')
                            ->label('Role Member')
                            ->relationship('roles', 'name')
                            ->options([
                                'member' => 'Member',
                                'premium_member' => 'Premium Member',
                                'vip_member' => 'VIP Member',
                            ])
                            ->default('member')
                            ->preload()
                            ->searchable(),

                        Forms\Components\FileUpload::make('foto')
                            ->label('Foto Profil')
                            ->image()
                            ->directory('member-photos')
                            ->visibility('public'),

                        Forms\Components\Textarea::make('catatan')
                            ->label('Catatan')
                            ->rows(3),
                    ])
                    ->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\ImageColumn::make('foto')
                    ->label('Foto')
                    ->circular()
                    ->defaultImageUrl(fn () => 'https://ui-avatars.com/api/?name=Member&color=7F9CF5&background=EBF4FF'),

                Tables\Columns\TextColumn::make('kode_member')
                    ->label('Kode Member')
                    ->searchable()
                    ->sortable()
                    ->copyable(),

                Tables\Columns\TextColumn::make('nama')
                    ->label('Nama')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('email')
                    ->label('Email')
                    ->searchable()
                    ->copyable(),

                Tables\Columns\TextColumn::make('telpon')
                    ->label('Telepon')
                    ->searchable(),

               
                Tables\Columns\TextColumn::make('roles.name')
                    ->label('Role')
                    ->badge()
                    ->formatStateUsing(fn ($state) => ucfirst(str_replace('_', ' ', $state)))
                    ->colors([
                        'primary' => 'member',
                        'warning' => 'premium_member',
                        'success' => 'vip_member',
                    ]),

              
            ])
            ->filters([
         

         
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                

                
                ]),
            ])
            ->defaultSort('id', 'desc');
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Components\Section::make('Informasi Member')
                    ->schema([
                        Components\ImageEntry::make('foto')
                            ->label('Foto Profil')
                            ->circular()
                            ->defaultImageUrl(fn () => 'https://ui-avatars.com/api/?name=Member&color=7F9CF5&background=EBF4FF'),

                        Components\TextEntry::make('kode_member')
                            ->label('Kode Member')
                            ->copyable(),

                        Components\TextEntry::make('nama')
                            ->label('Nama Lengkap'),

                        Components\TextEntry::make('email')
                            ->label('Email')
                            ->copyable(),

                        Components\TextEntry::make('telpon')
                            ->label('Telepon'),

                        Components\TextEntry::make('tanggal_lahir')
                            ->label('Tanggal Lahir')
                            ->date(),

                        Components\TextEntry::make('jenis_kelamin')
                            ->label('Jenis Kelamin')
                            ->formatStateUsing(fn ($state) => $state === 'L' ? 'Laki-laki' : 'Perempuan'),

                        Components\TextEntry::make('pekerjaan')
                            ->label('Pekerjaan'),

                        Components\TextEntry::make('roles.name')
                            ->label('Role Member')
                            ->badge()
                            ->formatStateUsing(fn ($state) => ucfirst(str_replace('_', ' ', $state)))
                            ->color(fn ($state) => match($state) {
                                'member' => 'primary',
                                'premium_member' => 'warning',
                                'vip_member' => 'success',
                                default => 'secondary'
                            }),
                    ])
                    ->columns(2),

                Components\Section::make('Alamat')
                    ->schema([
                        Components\TextEntry::make('alamat')
                            ->label('Alamat Lengkap'),

                        Components\TextEntry::make('kota')
                            ->label('Kota'),

                        Components\TextEntry::make('provinsi')
                            ->label('Provinsi'),

                        Components\TextEntry::make('kode_pos')
                            ->label('Kode Pos'),
                    ])
                    ->columns(2),

                Components\Section::make('Status & Aktivitas')
                    ->schema([
                   

                        Components\TextEntry::make('poin')
                            ->label('Total Poin')
                            ->numeric(),

                        Components\TextEntry::make('tanggal_bergabung')
                            ->label('Tanggal Bergabung')
                            ->date(),

                        Components\TextEntry::make('last_login_at')
                            ->label('Login Terakhir')
                            ->dateTime()
                            ->since(),

                        Components\TextEntry::make('catatan')
                            ->label('Catatan')
                            ->columnSpanFull(),
                    ])
                    ->columns(2),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListMembers::route('/'),
            'create' => Pages\CreateMember::route('/create'),
            'view' => Pages\ViewMember::route('/{record}'),
            'edit' => Pages\EditMember::route('/{record}/edit'),
        ];
    }

    // ========== PERMISSION CHECKING ==========

    public static function canViewAny(): bool
    {
        return Auth::check() && (Auth::user()->hasRole('super-admin') || Auth::user()->can('view_any_members'));
    }

    public static function canCreate(): bool
    {
        return Auth::check() && (Auth::user()->hasRole('super-admin') || Auth::user()->can('create_members'));
    }

    public static function canEdit(Model $record): bool
    {
        return Auth::check() && (Auth::user()->hasRole('super-admin') || Auth::user()->can('update_members'));
    }

    public static function canDelete(Model $record): bool
    {
        return Auth::check() && (Auth::user()->hasRole('super-admin') || Auth::user()->can('delete_members'));
    }

    public static function canDeleteAny(): bool
    {
        return Auth::check() && (Auth::user()->hasRole('super-admin') || Auth::user()->can('delete_any_members'));
    }

    public static function canView(Model $record): bool
    {
        return Auth::check() && (Auth::user()->hasRole('super-admin') || Auth::user()->can('view_members'));
    }
}
