<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class TestMemberRegistration extends Command
{
    protected $signature = 'test:member-registration';
    protected $description = 'Test member registration with default role assignment';

    public function handle()
    {
        $this->info("=== TESTING MEMBER REGISTRATION ===");
        
        // Test data
        $testData = [
            'name' => 'Test Member ' . now()->format('His'),
            'email' => 'testmember' . now()->format('His') . '@example.com',
            'password' => Hash::make('password123'),
        ];
        
        $this->info("Creating new user with data:");
        $this->info("- Name: {$testData['name']}");
        $this->info("- Email: {$testData['email']}");
        
        try {
            // Buat user baru
            $user = User::create($testData);
            
            $this->info("\n✓ User created successfully!");
            $this->info("- ID: {$user->id}");
            $this->info("- Name: {$user->name}");
            $this->info("- Email: {$user->email}");
            
            // Cek roles yang di-assign
            $roles = $user->roles->pluck('name')->toArray();
            
            if (empty($roles)) {
                $this->warn("⚠ No roles assigned to user");
            } else {
                $this->info("\n✓ Roles assigned:");
                foreach ($roles as $role) {
                    $this->info("- {$role}");
                }
                
                // Cek apakah role member ada
                if (in_array('member', $roles)) {
                    $this->info("\n✅ SUCCESS: Default role 'member' assigned correctly!");
                } else {
                    $this->warn("\n⚠ WARNING: Default role 'member' not assigned");
                }
            }
            
            // Test method isMember()
            if ($user->isMember()) {
                $this->info("✓ isMember() method returns true");
            } else {
                $this->warn("⚠ isMember() method returns false");
            }
            
            // Cleanup - hapus user test
            $this->info("\nCleaning up test user...");
            $user->delete();
            $this->info("✓ Test user deleted");
            
        } catch (\Exception $e) {
            $this->error("✗ Error creating user: " . $e->getMessage());
            return 1;
        }
        
        return 0;
    }
}
