# Default Role Assignment untuk Member

## 🎯 Tujuan
Dokumen ini menjelaskan implementasi default role assignment yang otomatis memberikan role `member` kepada setiap user baru yang mendaftar di sistem RajaMember.

## 🔧 Implementasi

### 1. **User Model Boot Event**
Default role assignment diimplementasikan di User model menggunakan Eloquent event `created`:

```php
// app/Models/User.php
protected static function boot()
{
    parent::boot();

    static::created(function ($user) {
        // Assign default role 'member' setelah user dibuat jika belum punya role
        if (!$user->roles()->exists()) {
            $user->assignRole('member');
        }
    });
}
```

### 2. **Halaman Pendaftaran RajaMember**
Pendaftaran melalui halaman admin tidak perlu assignment manual:

```php
// Modules/RajaMember/app/Filament/rajamember/Pages/PendaftaranMember.php
// Buat member baru
$member = User::create($data);
// Role 'member' sudah otomatis di-assign melalui User model boot event
```

### 3. **Halaman Register Auth**
Pendaftaran melalui halaman register publik juga otomatis:

```php
// Modules/RajaMember/app/Filament/rajamember/Pages/Auth/Register.php
// Buat member baru
$member = User::create($data);
// Role 'member' sudah otomatis di-assign melalui User model boot event
```

### 4. **Method Helper isMember()**
User model dilengkapi dengan method helper untuk cek status member:

```php
// app/Models/User.php
public function isMember(): bool
{
    return $this->hasRole('member');
}
```

## 🚀 Cara Kerja

### **Alur Default Role Assignment:**

1. **User baru dibuat** melalui `User::create()`
2. **Event `created` dipicu** secara otomatis
3. **Sistem cek** apakah user sudah memiliki role
4. **Jika belum ada role**, assign role `member`
5. **User langsung memiliki** role `member`

### **Kondisi Assignment:**

- ✅ **Otomatis assign** jika user belum memiliki role apapun
- ✅ **Tidak override** jika user sudah memiliki role lain
- ✅ **Berlaku untuk semua** cara pembuatan user (admin, register, API, dll)

## 📋 Testing

### **Test Command:**
```bash
php artisan test:member-registration
```

### **Expected Results:**
```
=== TESTING MEMBER REGISTRATION ===
Creating new user with data:
- Name: Test Member 112943
- Email: <EMAIL>

✓ User created successfully!
- ID: 16
- Name: Test Member 112943
- Email: <EMAIL>

✓ Roles assigned:
- member

✅ SUCCESS: Default role 'member' assigned correctly!
✓ isMember() method returns true

Cleaning up test user...
✓ Test user deleted
```

## 🎯 Keuntungan Implementasi

### **1. Otomatis dan Konsisten**
- Setiap user baru pasti mendapat role `member`
- Tidak perlu manual assignment di setiap tempat
- Konsisten di semua cara pembuatan user

### **2. Tidak Mengganggu Role Lain**
- Hanya assign jika user belum punya role
- Tidak override role yang sudah ada
- Admin tetap bisa assign role lain

### **3. Mudah Maintenance**
- Centralized di User model
- Mudah diubah jika diperlukan
- Tidak tersebar di berbagai file

## 🔍 Use Cases

### **1. Pendaftaran Member Baru**
```php
// Otomatis dapat role member
$member = User::create([
    'name' => 'John Doe',
    'email' => '<EMAIL>',
    'password' => bcrypt('password')
]);

// $member->hasRole('member') = true
// $member->isMember() = true
```

### **2. Pembuatan User oleh Admin**
```php
// Admin buat user tanpa role
$user = User::create($userData);
// Otomatis dapat role member

// Admin buat user dengan role tertentu
$user = User::create($userData);
$user->assignRole('admin'); // Override role member
```

### **3. Import/Seeder User**
```php
// Bulk import user
foreach ($userData as $data) {
    $user = User::create($data);
    // Setiap user otomatis dapat role member
}
```

## ⚙️ Konfigurasi

### **Mengubah Default Role:**
Jika ingin mengubah default role dari `member` ke role lain:

```php
// app/Models/User.php
static::created(function ($user) {
    if (!$user->roles()->exists()) {
        $user->assignRole('customer'); // Ganti ke role lain
    }
});
```

### **Disable Default Role:**
Jika ingin menonaktifkan default role assignment:

```php
// app/Models/User.php
static::created(function ($user) {
    // Comment atau hapus logic assignment
    // if (!$user->roles()->exists()) {
    //     $user->assignRole('member');
    // }
});
```

### **Conditional Assignment:**
Jika ingin assignment berdasarkan kondisi tertentu:

```php
// app/Models/User.php
static::created(function ($user) {
    if (!$user->roles()->exists()) {
        // Assign berdasarkan email domain
        if (str_ends_with($user->email, '@admin.com')) {
            $user->assignRole('admin');
        } else {
            $user->assignRole('member');
        }
    }
});
```

## 🛡️ Security Notes

1. **Role Validation:** Pastikan role `member` sudah ada di database
2. **Permission Control:** Role `member` harus memiliki permission yang sesuai
3. **Override Protection:** System tidak akan override role yang sudah ada

## 📝 Troubleshooting

### **Role 'member' tidak di-assign:**
1. Cek apakah role `member` ada di database
2. Cek apakah User model menggunakan trait `HasRoles`
3. Cek apakah event listener berjalan dengan benar

### **User sudah punya role lain:**
- System tidak akan assign role `member` jika user sudah punya role
- Ini adalah behavior yang diinginkan untuk mencegah override

### **Testing Assignment:**
```php
// Test manual
$user = User::create($testData);
dd($user->roles->pluck('name')); // Should contain 'member'
```
