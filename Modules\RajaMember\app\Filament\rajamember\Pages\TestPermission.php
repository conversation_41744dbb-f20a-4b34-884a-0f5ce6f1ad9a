<?php

namespace Modules\RajaMember\Filament\rajamember\Pages;

use Filament\Pages\Page;
use Illuminate\Support\Facades\Auth;

class TestPermission extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-shield-check';
    protected static string $view = 'rajamember::filament.pages.test-permission';
    protected static ?string $title = 'Test Permission';
    protected static ?string $navigationLabel = 'Test Permission';
    protected static ?string $navigationGroup = 'Member';
    protected static ?int $navigationSort = 99;

    public function mount(): void
    {
        $user = Auth::user();
        
        $this->data = [
            'user_name' => $user->name,
            'user_roles' => $user->roles->pluck('name')->toArray(),
            'user_permissions' => $user->getAllPermissions()->pluck('name')->toArray(),
            'can_view_members' => $user->can('view_members'),
            'can_view_any_members' => $user->can('view_any_members'),
            'can_create_members' => $user->can('create_members'),
            'can_update_members' => $user->can('update_members'),
            'can_delete_members' => $user->can('delete_members'),
            'can_view_dashboard_member' => $user->can('view_dashboard_member'),
            'has_role_member' => $user->hasRole('member'),
            'has_role_superadmin' => $user->hasRole('super-admin'),
        ];
    }

    public ?array $data = [];

    // ========== PERMISSION CHECKING ==========

    public static function canAccess(): bool
    {
        return Auth::check(); // Semua user yang login bisa akses halaman test ini
    }
}
