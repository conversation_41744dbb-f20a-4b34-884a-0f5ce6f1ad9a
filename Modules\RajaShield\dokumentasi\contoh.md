# Contoh Implementasi RajaShield

## Daftar Isi

1. [Setup Awal](#setup-awal)
2. [Contoh Role & Permission](#contoh-role--permission)
3. [Implementasi di FilamentPHP](#implementasi-di-filamentphp)
4. [Middleware Custom](#middleware-custom)
5. [Use Cases Umum](#use-cases-umum)
6. [Best Practices](#best-practices)

## Setup Awal

### 1. Konfigurasi User Model

```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Spatie\Permission\Traits\HasRoles;

class User extends Authenticatable
{
    use HasFactory, Notifiable, HasRoles;

    protected $fillable = [
        'name',
        'email',
        'password',
    ];

    protected $hidden = [
        'password',
        'remember_token',
    ];

    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
        ];
    }

    /**
     * Check if user is super admin
     */
    public function isSuperAdmin(): bool
    {
        return $this->hasRole('super-admin');
    }

    /**
     * Get user's primary role
     */
    public function getPrimaryRole(): ?string
    {
        return $this->roles->first()?->name;
    }

    /**
     * Check if user can access admin panel
     */
    public function canAccessPanel(): bool
    {
        return $this->hasAnyRole(['super-admin', 'admin', 'editor']);
    }
}
```

### 2. Seeder untuk Data Awal

```php
<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Modules\RajaShield\Models\Role;

class UserRoleSeeder extends Seeder
{
    public function run(): void
    {
        // Buat user super admin
        $superAdmin = User::create([
            'name' => 'Super Admin',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'email_verified_at' => now(),
        ]);
        $superAdmin->assignRole('super-admin');

        // Buat user admin
        $admin = User::create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'email_verified_at' => now(),
        ]);
        $admin->assignRole('admin');

        // Buat user editor
        $editor = User::create([
            'name' => 'Editor User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'email_verified_at' => now(),
        ]);
        $editor->assignRole('editor');

        // Buat user biasa
        $user = User::create([
            'name' => 'Regular User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'email_verified_at' => now(),
        ]);
        $user->assignRole('user');
    }
}
```

## Contoh Role & Permission

### 1. Setup Role Hierarchy

```php
<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Modules\RajaShield\Models\Role;
use Modules\RajaShield\Models\Permission;

class CustomRolePermissionSeeder extends Seeder
{
    public function run(): void
    {
        // Buat role custom
        $roles = [
            [
                'name' => 'content-manager',
                'description' => 'Mengelola konten website',
                'permissions' => [
                    'view_any_posts',
                    'view_posts',
                    'create_posts',
                    'update_posts',
                    'delete_posts',
                    'view_any_categories',
                    'view_categories',
                    'create_categories',
                    'update_categories',
                ]
            ],
            [
                'name' => 'sales-manager',
                'description' => 'Mengelola penjualan dan produk',
                'permissions' => [
                    'view_any_products',
                    'view_products',
                    'create_products',
                    'update_products',
                    'view_any_orders',
                    'view_orders',
                    'update_orders',
                    'view_any_customers',
                    'view_customers',
                ]
            ],
            [
                'name' => 'moderator',
                'description' => 'Moderasi konten dan user',
                'permissions' => [
                    'view_any_posts',
                    'view_posts',
                    'update_posts',
                    'view_any_comments',
                    'view_comments',
                    'update_comments',
                    'delete_comments',
                    'view_any_users',
                    'view_users',
                ]
            ]
        ];

        foreach ($roles as $roleData) {
            $role = Role::create([
                'name' => $roleData['name'],
                'description' => $roleData['description'],
            ]);

            // Assign permissions yang ada
            $existingPermissions = Permission::whereIn('name', $roleData['permissions'])->get();
            $role->givePermissionTo($existingPermissions);
        }
    }
}
```

### 2. Custom Permission untuk Model

```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Post extends Model
{
    protected $fillable = [
        'title',
        'content',
        'status',
        'user_id',
        'category_id',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class);
    }

    /**
     * Check if user can edit this post
     */
    public function canEdit(User $user): bool
    {
        // Super admin dapat edit semua
        if ($user->hasRole('super-admin')) {
            return true;
        }

        // Pemilik post dapat edit
        if ($this->user_id === $user->id) {
            return $user->can('update_own_posts');
        }

        // Editor dapat edit post orang lain
        return $user->can('update_posts');
    }

    /**
     * Check if user can delete this post
     */
    public function canDelete(User $user): bool
    {
        if ($user->hasRole('super-admin')) {
            return true;
        }

        if ($this->user_id === $user->id) {
            return $user->can('delete_own_posts');
        }

        return $user->can('delete_posts');
    }
}
```

## Implementasi di FilamentPHP

### 1. Resource dengan Permission Check

```php
<?php

namespace App\Filament\Resources;

use App\Filament\Resources\PostResource\Pages;
use App\Models\Post;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

class PostResource extends Resource
{
    protected static ?string $model = Post::class;
    protected static ?string $navigationIcon = 'heroicon-o-document-text';
    protected static ?string $navigationGroup = 'Content';

    // Permission checks
    public static function canViewAny(): bool
    {
        return auth()->user()->can('view_any_posts');
    }

    public static function canCreate(): bool
    {
        return auth()->user()->can('create_posts');
    }

    public static function canEdit(Model $record): bool
    {
        return $record->canEdit(auth()->user());
    }

    public static function canDelete(Model $record): bool
    {
        return $record->canDelete(auth()->user());
    }

    public static function canDeleteAny(): bool
    {
        return auth()->user()->can('delete_any_posts');
    }

    // Query modification berdasarkan role
    public static function getEloquentQuery(): Builder
    {
        $query = parent::getEloquentQuery();

        // Jika bukan admin/editor, hanya tampilkan post milik sendiri
        if (!auth()->user()->hasAnyRole(['super-admin', 'admin', 'editor'])) {
            $query->where('user_id', auth()->id());
        }

        return $query;
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('title')
                    ->required()
                    ->maxLength(255),
                
                Forms\Components\Textarea::make('content')
                    ->required()
                    ->columnSpanFull(),
                
                Forms\Components\Select::make('category_id')
                    ->relationship('category', 'name')
                    ->required(),
                
                Forms\Components\Select::make('status')
                    ->options([
                        'draft' => 'Draft',
                        'published' => 'Published',
                        'archived' => 'Archived',
                    ])
                    ->default('draft')
                    ->required(),
                
                // Hanya admin yang bisa mengubah pemilik
                Forms\Components\Select::make('user_id')
                    ->relationship('user', 'name')
                    ->default(auth()->id())
                    ->visible(fn () => auth()->user()->hasAnyRole(['super-admin', 'admin']))
                    ->required(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('title')
                    ->searchable()
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('user.name')
                    ->label('Author')
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('category.name')
                    ->sortable(),
                
                Tables\Columns\BadgeColumn::make('status')
                    ->colors([
                        'secondary' => 'draft',
                        'success' => 'published',
                        'danger' => 'archived',
                    ]),
                
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->options([
                        'draft' => 'Draft',
                        'published' => 'Published',
                        'archived' => 'Archived',
                    ]),
                
                Tables\Filters\SelectFilter::make('category')
                    ->relationship('category', 'name'),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPosts::route('/'),
            'create' => Pages\CreatePost::route('/create'),
            'edit' => Pages\EditPost::route('/{record}/edit'),
        ];
    }
}
```

### 2. Custom Page dengan Permission

```php
<?php

namespace App\Filament\Pages;

use Filament\Pages\Page;

class Analytics extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-chart-bar';
    protected static ?string $navigationGroup = 'Reports';
    protected static string $view = 'filament.pages.analytics';

    // Hanya admin dan manager yang bisa akses
    public static function canAccess(): bool
    {
        return auth()->user()->hasAnyRole(['super-admin', 'admin', 'sales-manager']);
    }

    public function mount(): void
    {
        // Additional permission check
        if (!auth()->user()->can('view_analytics')) {
            abort(403);
        }
    }
}
```

### 3. Widget dengan Permission

```php
<?php

namespace App\Filament\Widgets;

use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class StatsOverview extends BaseWidget
{
    // Hanya user dengan permission tertentu yang bisa lihat
    public static function canView(): bool
    {
        return auth()->user()->can('view_dashboard_stats');
    }

    protected function getStats(): array
    {
        $stats = [];

        // Total Users - hanya admin
        if (auth()->user()->can('view_any_users')) {
            $stats[] = Stat::make('Total Users', \App\Models\User::count())
                ->description('Registered users')
                ->descriptionIcon('heroicon-m-users')
                ->color('success');
        }

        // Total Posts - content manager dan admin
        if (auth()->user()->can('view_any_posts')) {
            $stats[] = Stat::make('Total Posts', \App\Models\Post::count())
                ->description('Published posts')
                ->descriptionIcon('heroicon-m-document-text')
                ->color('info');
        }

        // Revenue - hanya sales manager dan admin
        if (auth()->user()->can('view_sales_data')) {
            $stats[] = Stat::make('Revenue', 'Rp 1,234,567')
                ->description('This month')
                ->descriptionIcon('heroicon-m-currency-dollar')
                ->color('warning');
        }

        return $stats;
    }
}
```

## Middleware Custom

### 1. Role-based Middleware

```php
<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class CheckRole
{
    public function handle(Request $request, Closure $next, ...$roles)
    {
        if (!auth()->check()) {
            return redirect()->route('login');
        }

        if (!auth()->user()->hasAnyRole($roles)) {
            abort(403, 'Unauthorized. Required roles: ' . implode(', ', $roles));
        }

        return $next($request);
    }
}
```

### 2. Permission-based Middleware

```php
<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class CheckPermission
{
    public function handle(Request $request, Closure $next, ...$permissions)
    {
        if (!auth()->check()) {
            return redirect()->route('login');
        }

        if (!auth()->user()->hasAnyPermission($permissions)) {
            abort(403, 'Unauthorized. Required permissions: ' . implode(', ', $permissions));
        }

        return $next($request);
    }
}
```

### 3. Registrasi Middleware

```php
<?php

namespace App\Http;

use Illuminate\Foundation\Http\Kernel as HttpKernel;

class Kernel extends HttpKernel
{
    protected $middlewareAliases = [
        // ... middleware lain
        'role' => \App\Http\Middleware\CheckRole::class,
        'permission' => \App\Http\Middleware\CheckPermission::class,
    ];
}
```

### 4. Penggunaan di Routes

```php
<?php

use Illuminate\Support\Facades\Route;

// Routes untuk admin saja
Route::middleware(['auth', 'role:admin,super-admin'])->group(function () {
    Route::get('/admin/users', [UserController::class, 'index']);
    Route::get('/admin/settings', [SettingController::class, 'index']);
});

// Routes berdasarkan permission
Route::middleware(['auth', 'permission:manage_content'])->group(function () {
    Route::resource('posts', PostController::class);
    Route::resource('categories', CategoryController::class);
});

// Routes untuk sales manager
Route::middleware(['auth', 'role:sales-manager,admin'])->group(function () {
    Route::get('/sales/dashboard', [SalesController::class, 'dashboard']);
    Route::get('/sales/reports', [SalesController::class, 'reports']);
});
```

## Use Cases Umum

### 1. Multi-tenant Application

```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Tenant extends Model
{
    protected $fillable = ['name', 'domain', 'settings'];

    public function users()
    {
        return $this->hasMany(User::class);
    }

    /**
     * Assign role to user within tenant context
     */
    public function assignUserRole(User $user, string $role): void
    {
        // Create tenant-specific role if not exists
        $tenantRole = Role::firstOrCreate([
            'name' => "{$role}@{$this->id}",
            'description' => "{$role} for tenant {$this->name}",
        ]);

        $user->assignRole($tenantRole);
    }
}
```

### 2. Content Approval Workflow

```php
<?php

namespace App\Models;

class Post extends Model
{
    const STATUS_DRAFT = 'draft';
    const STATUS_PENDING = 'pending';
    const STATUS_APPROVED = 'approved';
    const STATUS_REJECTED = 'rejected';

    public function submitForApproval(): bool
    {
        if ($this->status !== self::STATUS_DRAFT) {
            return false;
        }

        $this->update(['status' => self::STATUS_PENDING]);
        return true;
    }

    public function approve(User $user): bool
    {
        if (!$user->can('approve_posts')) {
            return false;
        }

        $this->update(['status' => self::STATUS_APPROVED]);
        return true;
    }

    public function reject(User $user, string $reason = ''): bool
    {
        if (!$user->can('approve_posts')) {
            return false;
        }

        $this->update([
            'status' => self::STATUS_REJECTED,
            'rejection_reason' => $reason,
        ]);
        return true;
    }
}
```

### 3. Department-based Permissions

```php
<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Modules\RajaShield\Models\Role;
use Modules\RajaShield\Models\Permission;

class DepartmentRoleSeeder extends Seeder
{
    public function run(): void
    {
        $departments = [
            'hr' => [
                'name' => 'HR Manager',
                'permissions' => [
                    'view_any_employees',
                    'create_employees',
                    'update_employees',
                    'view_payroll',
                    'manage_attendance',
                ]
            ],
            'finance' => [
                'name' => 'Finance Manager',
                'permissions' => [
                    'view_financial_reports',
                    'manage_invoices',
                    'view_payroll',
                    'manage_expenses',
                ]
            ],
            'it' => [
                'name' => 'IT Manager',
                'permissions' => [
                    'manage_system_settings',
                    'view_system_logs',
                    'manage_user_accounts',
                    'backup_restore',
                ]
            ]
        ];

        foreach ($departments as $dept => $data) {
            $role = Role::create([
                'name' => "{$dept}-manager",
                'description' => $data['name'],
            ]);

            // Create permissions if not exist
            foreach ($data['permissions'] as $permissionName) {
                $permission = Permission::firstOrCreate([
                    'name' => $permissionName,
                    'description' => ucwords(str_replace('_', ' ', $permissionName)),
                    'group' => $dept,
                ]);

                $role->givePermissionTo($permission);
            }
        }
    }
}
```

## Best Practices

### 1. Naming Convention

```php
// Role names: kebab-case
'super-admin', 'content-manager', 'sales-manager'

// Permission names: snake_case dengan pattern action_target
'view_any_posts', 'create_posts', 'update_own_posts'
'manage_user_roles', 'access_admin_panel'

// Group names: singular, lowercase
'resource', 'page', 'widget', 'system'
```

### 2. Permission Granularity

```php
// ✅ Good: Specific permissions
'view_published_posts'
'edit_own_posts'
'delete_any_posts'
'approve_pending_posts'

// ❌ Avoid: Too broad permissions
'manage_everything'
'admin_access'
'full_control'
```

### 3. Role Hierarchy

```php
// ✅ Good: Clear hierarchy
'super-admin' -> semua permissions
'admin' -> management permissions
'manager' -> department-specific permissions
'editor' -> content permissions
'user' -> basic permissions

// ❌ Avoid: Confusing hierarchy
'admin-level-1', 'admin-level-2', 'admin-level-3'
```

### 4. Caching Strategy

```php
// Cache permissions untuk performa
public function getUserPermissions(User $user): Collection
{
    return Cache::remember(
        "user.{$user->id}.permissions",
        3600, // 1 hour
        fn() => $user->getAllPermissions()
    );
}

// Clear cache saat update
public function updateUserRole(User $user, string $role): void
{
    $user->syncRoles([$role]);
    Cache::forget("user.{$user->id}.permissions");
}
```

### 5. Error Handling

```php
<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Gate;
use Spatie\Permission\Exceptions\UnauthorizedException;

class PostController extends Controller
{
    public function index()
    {
        try {
            // Check permission
            if (!auth()->user()->can('view_any_posts')) {
                throw UnauthorizedException::forPermissions(['view_any_posts']);
            }

            $posts = Post::paginate(10);
            return view('posts.index', compact('posts'));

        } catch (UnauthorizedException $e) {
            return redirect()->back()
                ->with('error', 'Anda tidak memiliki izin untuk melihat daftar post.');
        }
    }

    public function store(Request $request)
    {
        Gate::authorize('create_posts');

        // Validation dan store logic
        $post = Post::create($request->validated());

        return redirect()->route('posts.index')
            ->with('success', 'Post berhasil dibuat.');
    }
}
```

### 6. Testing Permissions

```php
<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Post;
use Modules\RajaShield\Models\Role;
use Tests\TestCase;

class PostPermissionTest extends TestCase
{
    public function test_admin_can_view_all_posts()
    {
        $admin = User::factory()->create();
        $admin->assignRole('admin');

        $response = $this->actingAs($admin)
            ->get('/admin/posts');

        $response->assertStatus(200);
    }

    public function test_user_cannot_delete_others_posts()
    {
        $user = User::factory()->create();
        $user->assignRole('user');

        $otherUser = User::factory()->create();
        $post = Post::factory()->create(['user_id' => $otherUser->id]);

        $response = $this->actingAs($user)
            ->delete("/admin/posts/{$post->id}");

        $response->assertStatus(403);
    }

    public function test_editor_can_edit_any_post()
    {
        $editor = User::factory()->create();
        $editor->assignRole('editor');

        $post = Post::factory()->create();

        $response = $this->actingAs($editor)
            ->put("/admin/posts/{$post->id}", [
                'title' => 'Updated Title',
                'content' => 'Updated content',
            ]);

        $response->assertStatus(200);
        $this->assertDatabaseHas('posts', [
            'id' => $post->id,
            'title' => 'Updated Title',
        ]);
    }
}
```

Lanjutkan ke [API Reference](api-reference.md) untuk dokumentasi API lengkap.
