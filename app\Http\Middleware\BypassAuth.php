<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use App\Models\User;
use Symfony\Component\HttpFoundation\Response;

class BypassAuth
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Periksa apakah lingkungan adalah local
        if (app()->environment('local')) {
            // Periksa apakah ada parameter khusus untuk bypass autentikasi
            if ($request->has('augment_access') && $request->augment_access === 'true') {
                // Jika belum login, login sebagai superadmin atau user pertama
                if (!Auth::check()) {
                    // Cari user dengan role super-admin atau user pertama
                    $user = User::role('super-admin')->first() ?? User::first();

                    if ($user) {
                        Auth::login($user);
                        // Tambahkan log untuk debugging
                        Log::info('Bypass Auth: Login berhasil sebagai ' . $user->name);

                        // Jika ini adalah request ke panel hotel, redirect ke dashboard hotel
                        $path = $request->path();
                        if (Str::startsWith($path, 'adminhotel')) {
                            // Jika ini adalah halaman login hotel, redirect ke dashboard hotel
                            if (Str::endsWith($path, 'login')) {
                                return redirect()->to('/adminhotel');
                            }
                        }
                    } else {
                        Log::warning('Bypass Auth: Tidak dapat menemukan user untuk login otomatis');
                    }
                } else {
                    Log::info('Bypass Auth: User sudah login sebagai ' . Auth::user()->name);
                }
            }
        } else {
            // Jika bukan lingkungan local dan ada parameter augment_access, log peringatan
            if ($request->has('augment_access') && $request->augment_access === 'true') {
                Log::warning('Bypass Auth: Percobaan bypass autentikasi di lingkungan non-local ditolak');
            }
        }

        return $next($request);
    }
}
