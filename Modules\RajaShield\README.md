# 🛡️ RajaShield - Role & Permission Management

[![<PERSON><PERSON>](https://img.shields.io/badge/Laravel-11.x-red.svg)](https://laravel.com)
[![FilamentPHP](https://img.shields.io/badge/FilamentPHP-3.2.x-orange.svg)](https://filamentphp.com)
[![PHP](https://img.shields.io/badge/PHP-8.1+-blue.svg)](https://php.net)
[![License](https://img.shields.io/badge/License-MIT-green.svg)](LICENSE.md)

RajaShield adalah modul Laravel yang powerful untuk mengelola sistem role dan permission menggunakan Spatie Laravel-Permission. Dirancang khusus untuk integrasi seamless dengan FilamentPHP dan menyediakan interface yang user-friendly untuk manajemen hak akses.

## ✨ Fitur Utama

- 🔐 **Role & Permission Management** - CRUD lengkap dengan interface yang intuitif
- 🔄 **Auto-Discovery & Sync** - Deteksi otomatis FilamentPHP Resources, Pages, Widgets
- 🎨 **FilamentPHP Integration** - Integrasi native dengan tabbed interface dan grid layout
- 🛡️ **Security Features** - Super admin role, permission validation, audit logging
- ⚡ **Performance Optimized** - Caching, eager loading, optimized queries
- 🏗️ **Modular Architecture** - Struktur modular yang mudah di-maintain

## 🚀 Quick Start

```bash
# 1. Enable modul
php artisan module:enable RajaShield

# 2. Jalankan migrasi
php artisan module:migrate RajaShield

# 3. Jalankan seeder
php artisan module:seed RajaShield

# 4. Sync permissions
php artisan rajashield:sync-permissions --force

# 5. Assign super admin (opsional)
# php artisan tinker
# $user = User::find(1); $user->assignRole('super-admin');
```

**✅ Selesai!** Login ke admin panel dan cek menu RajaShield.

## 📖 Dokumentasi Lengkap

Dokumentasi lengkap tersedia di direktori `/dokumentasi`:

### 🎯 Untuk Pemula
- 🚀 [**Quick Start Guide**](dokumentasi/quick-start.md) - Setup dalam 5 menit
- 📖 [**Instalasi dan Konfigurasi**](dokumentasi/instalasi.md) - Panduan instalasi lengkap
- 🎯 [**Panduan Penggunaan**](dokumentasi/penggunaan.md) - Cara menggunakan semua fitur

### 💻 Untuk Developer
- 💡 [**Contoh Implementasi**](dokumentasi/contoh.md) - Contoh kode dan use cases
- 📚 [**API Reference**](dokumentasi/api-reference.md) - Dokumentasi API lengkap
- 🔧 [**Troubleshooting**](dokumentasi/troubleshooting.md) - Solusi masalah umum

### 📋 Referensi
- ❓ [**FAQ**](dokumentasi/faq.md) - Pertanyaan yang sering diajukan
- 📝 [**Changelog**](dokumentasi/changelog.md) - Riwayat perubahan versi
- 🚀 [**Deployment Guide**](dokumentasi/deployment.md) - Panduan deployment production

## 💡 Contoh Penggunaan

### Assign Role ke User
```php
$user = User::find(1);
$user->assignRole('admin');

// Multiple roles
$user->assignRole(['admin', 'editor']);
```

### Cek Permission
```php
// Di controller
if (auth()->user()->can('view_any_posts')) {
    // User dapat melihat posts
}

// Di blade
@can('edit_posts')
    <a href="{{ route('posts.edit', $post) }}">Edit</a>
@endcan
```

### FilamentPHP Resource
```php
public static function canViewAny(): bool
{
    return auth()->user()->can('view_any_posts');
}
```

## 🔄 Sync Permissions

RajaShield secara otomatis mendeteksi FilamentPHP Resources, Pages, dan Widgets:

```bash
# Preview sync (dry run)
php artisan rajashield:sync-permissions --dry-run

# Sync dengan konfirmasi
php artisan rajashield:sync-permissions

# Force sync tanpa konfirmasi
php artisan rajashield:sync-permissions --force
```

## 🛠️ Commands

```bash
# Module commands
php artisan module:enable RajaShield
php artisan module:disable RajaShield
php artisan module:migrate RajaShield
php artisan module:seed RajaShield

# Permission sync
php artisan rajashield:sync-permissions [--dry-run] [--force]
```

## 🔧 Konfigurasi

### User Model Setup
```php
use Spatie\Permission\Traits\HasRoles;

class User extends Authenticatable
{
    use HasRoles;
    // ...
}
```

### FilamentPHP Panel Registration
```php
// Di PanelProvider
->resources([
    \Modules\RajaShield\Filament\Resources\RoleResource::class,
    \Modules\RajaShield\Filament\Resources\PermissionResource::class,
])
->pages([
    \Modules\RajaShield\Filament\Pages\SyncPermissions::class,
])
```

## 📋 Requirements

- **Laravel**: 11.x
- **FilamentPHP**: 3.2.x
- **PHP**: 8.1+
- **Database**: MySQL 8.0+ / PostgreSQL 13+ / SQLite 3.35+

## 🤝 Kontribusi

Modul ini dikembangkan sebagai bagian dari ekosistem Laravel modular. Untuk kontribusi atau laporan bug, silakan hubungi tim development.

## 📄 Lisensi

RajaShield dilisensikan di bawah [MIT License](LICENSE.md).
