<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Filament\Resources\UserResource;
use Mo<PERSON>les\RajaMember\Filament\rajamember\Pages\Dashboard as MemberDashboard;
use Mo<PERSON>les\RajaMember\Filament\rajamember\Pages\PendaftaranMember;
use Modules\RajaMember\Filament\Resources\MemberResource;

class TestUserAccess extends Command
{
    protected $signature = 'test:user-access {user_id}';
    protected $description = 'Test user access to various FilamentPHP resources and pages';

    public function handle()
    {
        $userId = $this->argument('user_id');
        
        $user = User::find($userId);
        if (!$user) {
            $this->error("User with ID {$userId} not found");
            return 1;
        }

        $this->info("=== TESTING USER ACCESS ===");
        $this->info("User: {$user->name} (ID: {$user->id})");
        $this->info("Email: {$user->email}");
        $this->info("Roles: " . $user->roles->pluck('name')->join(', '));
        
        // Simulate login
        auth()->login($user);
        
        $this->info("\n=== ADMIN PANEL RESOURCES ===");
        
        // Test UserResource access
        $this->testResourceAccess('UserResource (Admin)', UserResource::class);
        
        $this->info("\n=== MEMBER PANEL RESOURCES ===");
        
        // Test MemberResource access
        $this->testResourceAccess('MemberResource (Member)', MemberResource::class);
        
        $this->info("\n=== MEMBER PANEL PAGES ===");
        
        // Test Member Dashboard access
        $this->testPageAccess('Member Dashboard', MemberDashboard::class);
        
        // Test PendaftaranMember access
        $this->testPageAccess('Pendaftaran Member', PendaftaranMember::class);
        
        // Logout
        auth()->logout();
        
        return 0;
    }
    
    private function testResourceAccess(string $name, string $resourceClass): void
    {
        try {
            $canViewAny = $resourceClass::canViewAny();
            $canCreate = $resourceClass::canCreate();
            
            $this->info("📋 {$name}:");
            $this->info("  - canViewAny(): " . ($canViewAny ? '✅ YES' : '❌ NO'));
            $this->info("  - canCreate(): " . ($canCreate ? '✅ YES' : '❌ NO'));
            
        } catch (\Exception $e) {
            $this->error("  - Error testing {$name}: " . $e->getMessage());
        }
    }
    
    private function testPageAccess(string $name, string $pageClass): void
    {
        try {
            $canAccess = $pageClass::canAccess();
            
            $this->info("📄 {$name}:");
            $this->info("  - canAccess(): " . ($canAccess ? '✅ YES' : '❌ NO'));
            
        } catch (\Exception $e) {
            $this->error("  - Error testing {$name}: " . $e->getMessage());
        }
    }
}
