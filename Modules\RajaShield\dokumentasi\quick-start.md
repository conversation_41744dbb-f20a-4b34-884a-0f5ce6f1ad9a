# Quick Start Guide - RajaShield

Panduan cepat untuk memulai menggunakan RajaShield dalam 5 menit.

## Prasyarat

Pastikan Anda sudah memiliki:
- ✅ Laravel 11.x terinstall
- ✅ FilamentPHP 3.2.x terinstall
- ✅ Database (MySQL/PostgreSQL/SQLite) terkonfigurasi
- ✅ PHP 8.1+ dengan extensions yang diperlukan

## Langkah 1: Instalasi (2 menit)

### Install Dependencies

```bash
# Install Spatie Permission (jika belum ada)
composer require spatie/laravel-permission

# Publish dan migrate Spatie Permission
php artisan vendor:publish --provider="Spatie\Permission\PermissionServiceProvider"
php artisan migrate
```

### Setup RajaShield

```bash
# Enable modul RajaShield
php artisan module:enable RajaShield

# Jalankan migrasi RajaShield
php artisan module:migrate RajaShield

# Jalankan seeder untuk data awal
php artisan module:seed RajaShield
```

## Langkah 2: Konfigurasi User Model (1 menit)

Edit file `app/Models/User.php`:

```php
<?php

namespace App\Models;

use Illuminate\Foundation\Auth\User as Authenticatable;
use Spatie\Permission\Traits\HasRoles;

class User extends Authenticatable
{
    use HasRoles; // Tambahkan trait ini
    
    // ... kode lainnya tetap sama
}
```

## Langkah 3: Sync Permissions (1 menit)

```bash
# Sync permissions dengan FilamentPHP entities
php artisan rajashield:sync-permissions --force
```

Output yang diharapkan:
```
🔄 Starting permission sync...
📊 Sync Summary:
+-----------------------+-------+
| Type                  | Count |
+-----------------------+-------+
| Current Permissions   | 0     |
| New Permissions Found | 45    |
| To Add                | 45    |
| To Remove             | 0     |
+-----------------------+-------+
✅ Permission sync completed successfully!
```

## Langkah 4: Buat Super Admin (1 menit)

```bash
# Masuk ke tinker
php artisan tinker
```

```php
// Di dalam tinker
$user = \App\Models\User::find(1);
$user->assignRole('superadmin');
exit
```

Atau buat user baru:

```php
// Di dalam tinker
$user = \App\Models\User::create([
    'name' => 'Super Admin',
    'email' => '<EMAIL>',
    'password' => bcrypt('password'),
    'email_verified_at' => now(),
]);
$user->assignRole('super-admin');
exit
```

## Langkah 5: Test & Verifikasi

### Login ke Admin Panel

1. Buka browser dan akses admin panel FilamentPHP
2. Login dengan user yang sudah di-assign role `super-admin`
3. Cek menu **RajaShield** tersedia dengan sub-menu:
   - 👥 **Roles**
   - 🔐 **Permissions** 
   - 🔄 **Sync Permissions**

### Test Permissions

```php
// Test di tinker atau controller
$user = auth()->user();

// Cek role
$user->hasRole('super-admin'); // Should return true

// Cek permission
$user->can('view_any_roles'); // Should return true

// Lihat semua permissions
$user->getAllPermissions()->pluck('name'); // Should show all permissions
```

## Penggunaan Dasar

### Membuat Role Baru

1. **Via Admin Panel**:
   - Akses **RajaShield > Roles**
   - Klik **Buat Peran**
   - Isi nama dan deskripsi
   - Pilih permissions yang diinginkan
   - Simpan

2. **Via Code**:
```php
use Modules\RajaShield\Models\Role;

$role = Role::create([
    'name' => 'editor',
    'description' => 'Content Editor',
]);

$role->givePermissionTo([
    'view_any_posts',
    'view_posts', 
    'create_posts',
    'update_posts',
]);
```

### Assign Role ke User

```php
// Assign single role
$user->assignRole('editor');

// Assign multiple roles
$user->assignRole(['editor', 'moderator']);

// Remove role
$user->removeRole('editor');

// Sync roles (replace all)
$user->syncRoles(['admin']);
```

### Cek Permission di Controller

```php
<?php

namespace App\Http\Controllers;

class PostController extends Controller
{
    public function index()
    {
        // Method 1: Using can()
        if (!auth()->user()->can('view_any_posts')) {
            abort(403);
        }
        
        // Method 2: Using Gate
        Gate::authorize('view_any_posts');
        
        // Method 3: Using middleware (di routes)
        // Route::middleware(['permission:view_any_posts'])->group(function () {
        //     Route::get('/posts', [PostController::class, 'index']);
        // });
        
        $posts = Post::paginate(10);
        return view('posts.index', compact('posts'));
    }
}
```

### Cek Permission di Blade

```blade
{{-- Cek role --}}
@role('admin')
    <div class="admin-panel">
        <p>Panel Admin</p>
    </div>
@endrole

{{-- Cek permission --}}
@can('edit_posts')
    <a href="{{ route('posts.edit', $post) }}" class="btn btn-primary">
        Edit Post
    </a>
@endcan

{{-- Cek multiple roles --}}
@hasanyrole('admin|editor')
    <div class="content-management">
        <p>Content Management Tools</p>
    </div>
@endhasanyrole
```

### FilamentPHP Resource dengan Permissions

```php
<?php

namespace App\Filament\Resources;

use Filament\Resources\Resource;
use Illuminate\Database\Eloquent\Model;

class PostResource extends Resource
{
    // Permission checks
    public static function canViewAny(): bool
    {
        return auth()->user()->can('view_any_posts');
    }

    public static function canCreate(): bool
    {
        return auth()->user()->can('create_posts');
    }

    public static function canEdit(Model $record): bool
    {
        return auth()->user()->can('update_posts');
    }

    public static function canDelete(Model $record): bool
    {
        return auth()->user()->can('delete_posts');
    }

    // Hide navigation jika tidak ada permission
    public static function shouldRegisterNavigation(): bool
    {
        return auth()->user()->can('view_any_posts');
    }
}
```

## Tips Cepat

### 1. Sync Permissions Otomatis
Tambahkan ke deployment script:
```bash
php artisan rajashield:sync-permissions --force
```

### 2. Backup Permissions
```bash
# Export current permissions
php artisan db:seed --class="PermissionBackupSeeder"
```

### 3. Debug Permissions
```php
// Lihat semua permissions user
dd(auth()->user()->getAllPermissions()->pluck('name'));

// Lihat permissions melalui roles
dd(auth()->user()->getPermissionsViaRoles()->pluck('name'));

// Lihat direct permissions
dd(auth()->user()->getDirectPermissions()->pluck('name'));
```

### 4. Clear Permission Cache
```bash
# Clear permission cache
php artisan permission:cache-reset

# Clear all cache
php artisan cache:clear
```

### 5. Performance Tips
```php
// Eager load permissions
$users = User::with('roles.permissions')->get();

// Cache permissions
$permissions = Cache::remember(
    "user.{$userId}.permissions",
    3600,
    fn() => $user->getAllPermissions()
);
```

## Troubleshooting Cepat

### Permission tidak muncul?
```bash
php artisan rajashield:sync-permissions --force
php artisan cache:clear
```

### User tidak bisa akses meskipun ada permission?
```bash
php artisan permission:cache-reset
```

### Error "Class not found"?
```bash
composer dump-autoload
php artisan config:clear
```

## Selanjutnya

Setelah setup dasar selesai, Anda bisa:

1. 📖 Baca [Panduan Penggunaan Lengkap](penggunaan.md)
2. 💡 Lihat [Contoh Implementasi](contoh.md)
3. 📚 Pelajari [API Reference](api-reference.md)
4. 🔧 Cek [Troubleshooting](troubleshooting.md) jika ada masalah

**Selamat! RajaShield sudah siap digunakan! 🎉**
