<?php

namespace Modules\RajaMember\Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class MemberPermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Permission untuk MemberResource
        $memberPermissions = [
            // Member Resource permissions
            ['name' => 'view_members', 'description' => 'View Members', 'group' => 'resource'],
            ['name' => 'view_any_members', 'description' => 'View Any Members', 'group' => 'resource'],
            ['name' => 'create_members', 'description' => 'Create Members', 'group' => 'resource'],
            ['name' => 'update_members', 'description' => 'Update Members', 'group' => 'resource'],
            ['name' => 'delete_members', 'description' => 'Delete Members', 'group' => 'resource'],
            ['name' => 'delete_any_members', 'description' => 'Delete Any Members', 'group' => 'resource'],
            
            // Member Pages permissions
            ['name' => 'view_dashboard_member', 'description' => 'View Dashboard Member', 'group' => 'page'],
            ['name' => 'view_pendaftaran_member', 'description' => 'View Pendaftaran Member', 'group' => 'page'],
            
            // Member Widgets permissions
            ['name' => 'view_widget_member_stats', 'description' => 'View Member Stats Widget', 'group' => 'widget'],
            ['name' => 'view_widget_user_info', 'description' => 'View User Info Widget', 'group' => 'widget'],
        ];

        // Buat permissions jika belum ada
        foreach ($memberPermissions as $permissionData) {
            Permission::firstOrCreate(
                ['name' => $permissionData['name']],
                [
                    'guard_name' => 'web',
                    'group' => $permissionData['group'],
                    'description' => $permissionData['description'],
                ]
            );
        }

        // Buat role member jika belum ada
        $memberRole = Role::firstOrCreate(
            ['name' => 'member'],
            ['description' => 'Regular Member']
        );

        // Assign basic permissions ke role member
        $memberRole->givePermissionTo([
            'view_dashboard_member',
            'view_widget_user_info',
        ]);

        // Assign semua permission ke super-admin
        $superAdminRole = Role::where('name', 'super-admin')->first();
        if ($superAdminRole) {
            $allMemberPermissions = Permission::whereIn('name', collect($memberPermissions)->pluck('name'))->get();
            $superAdminRole->givePermissionTo($allMemberPermissions);
        }

        $this->command->info('Member permissions berhasil ditambahkan: ' . count($memberPermissions) . ' permissions');
    }
}
