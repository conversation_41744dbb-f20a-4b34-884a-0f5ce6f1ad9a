<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class UserPermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Permission untuk UserResource (Karyawan)
        $userPermissions = [
            // User Resource permissions
            ['name' => 'view_users', 'description' => 'View Users', 'group' => 'resource'],
            ['name' => 'view_any_users', 'description' => 'View Any Users', 'group' => 'resource'],
            ['name' => 'create_users', 'description' => 'Create Users', 'group' => 'resource'],
            ['name' => 'update_users', 'description' => 'Update Users', 'group' => 'resource'],
            ['name' => 'delete_users', 'description' => 'Delete Users', 'group' => 'resource'],
            ['name' => 'delete_any_users', 'description' => 'Delete Any Users', 'group' => 'resource'],
        ];

        // Buat permissions jika belum ada
        foreach ($userPermissions as $permissionData) {
            Permission::firstOrCreate(
                ['name' => $permissionData['name']],
                [
                    'guard_name' => 'web',
                    'group' => $permissionData['group'],
                    'description' => $permissionData['description'],
                ]
            );
        }

        // Assign semua permission ke super-admin
        $superAdminRole = Role::where('name', 'super-admin')->first();
        if ($superAdminRole) {
            $allUserPermissions = Permission::whereIn('name', collect($userPermissions)->pluck('name'))->get();
            $superAdminRole->givePermissionTo($allUserPermissions);
        }

        $this->command->info('User permissions berhasil ditambahkan: ' . count($userPermissions) . ' permissions');
    }
}
