<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use Spatie\Permission\Models\Role;

class AssignSuperAdminRole extends Command
{
    protected $signature = 'user:assign-super-admin {user_id}';
    protected $description = 'Assign super-admin role to a user';

    public function handle()
    {
        $userId = $this->argument('user_id');
        
        $user = User::find($userId);
        if (!$user) {
            $this->error("User with ID {$userId} not found");
            return 1;
        }

        // Cek apakah role super-admin ada
        $superAdminRole = Role::where('name', 'super-admin')->first();
        if (!$superAdminRole) {
            $this->error("Role 'super-admin' not found");
            return 1;
        }

        // Assign role
        $user->syncRoles(['super-admin']);
        
        $this->info("Successfully assigned super-admin role to user: {$user->name}");
        $this->info("Current roles: " . $user->fresh()->roles->pluck('name')->join(', '));
        
        return 0;
    }
}
