# Dokumentasi Modul RajaShield

## Daftar Isi

1. [Pengenalan](#pengenalan)
2. [🚀 Quick Start Guide](quick-start.md) - **<PERSON><PERSON> dalam 5 menit!**
3. [📖 Instalasi dan Konfigurasi](instalasi.md)
4. [🎯 Panduan Penggunaan](penggunaan.md)
5. [💡 Contoh Implementasi](contoh.md)
6. [📚 API Reference](api-reference.md)
7. [🔧 Troubleshooting](troubleshooting.md)
8. [❓ FAQ](faq.md)
9. [📝 Changelog](changelog.md)

## Pengenalan

RajaShield adalah modul <PERSON><PERSON> yang powerful untuk mengelola sistem role dan permission menggunakan paket Spatie <PERSON>-Permission. Modul ini dirancang khusus untuk integrasi dengan FilamentPHP dan menyediakan interface yang user-friendly untuk manajemen hak akses.

### Fitur Utama

#### 🔐 Manajemen Role & Permission
- **Role Management**: <PERSON><PERSON><PERSON> peran pengguna dengan mudah
- **Permission Management**: Atur izin akses secara granular
- **Dual Permission System**: Mendukung route-based dan model-based permissions
- **Super Admin**: Role khusus dengan akses penuh ke semua fitur

#### 🔄 Auto-Discovery & Sync
- **Auto-Discovery**: Deteksi otomatis FilamentPHP Resources, Pages, dan Widgets
- **Permission Sync**: Sinkronisasi permissions dengan entities terbaru
- **Command Line Interface**: Tools CLI untuk maintenance
- **Web Interface**: Interface web untuk sync permissions

#### 🎨 FilamentPHP Integration
- **Native Integration**: Terintegrasi penuh dengan FilamentPHP
- **Tabbed Interface**: Organisasi permissions dalam tabs berdasarkan entity type
- **Grid Layout**: Layout 3-kolom untuk tampilan yang optimal
- **Real-time Updates**: Update permissions secara real-time

#### 🛡️ Security Features
- **Database Transactions**: Operasi sync menggunakan database transaction
- **Permission Validation**: Validasi permissions sebelum assignment
- **Role Hierarchy**: Sistem hierarki role yang fleksibel
- **Guard Support**: Mendukung multiple guards

### Arsitektur Modul

```
Modules/RajaShield/
├── app/
│   ├── Console/Commands/          # Artisan commands
│   ├── Filament/                  # FilamentPHP resources & pages
│   ├── Helpers/                   # Helper classes
│   ├── Models/                    # Eloquent models
│   └── Providers/                 # Service providers
├── config/                        # Konfigurasi modul
├── database/                      # Migrations & seeders
├── dokumentasi/                   # Dokumentasi lengkap
└── resources/                     # Views & assets
```

### Teknologi yang Digunakan

- **Laravel 11**: Framework utama
- **FilamentPHP 3.2**: Admin panel framework
- **Spatie Laravel-Permission**: Package untuk role & permission
- **Laravel Modules**: Struktur modular
- **Bootstrap 5**: Styling framework

### Kompatibilitas

- ✅ Laravel 11.x
- ✅ FilamentPHP 3.2.x
- ✅ PHP 8.1+
- ✅ MySQL 8.0+
- ✅ Spatie Laravel-Permission 6.x

### Keunggulan RajaShield

1. **Mudah Digunakan**: Interface yang intuitif dan user-friendly
2. **Highly Configurable**: Konfigurasi yang sangat fleksibel
3. **Auto-Discovery**: Deteksi otomatis entities baru
4. **Performance Optimized**: Optimasi performa untuk aplikasi besar
5. **Well Documented**: Dokumentasi lengkap dan contoh implementasi
6. **Modular Design**: Arsitektur modular yang mudah di-maintain

### Use Cases

- **Admin Panel**: Manajemen hak akses admin panel
- **Multi-tenant Applications**: Aplikasi dengan multiple tenant
- **Enterprise Applications**: Aplikasi enterprise dengan complex permissions
- **Content Management**: CMS dengan role-based access control
- **E-commerce Platforms**: Platform e-commerce dengan seller permissions

## 🚀 Quick Start

**Ingin langsung mulai?** Ikuti [Quick Start Guide](quick-start.md) untuk setup dalam 5 menit!

```bash
# 1. Enable modul
php artisan module:enable RajaShield

# 2. Jalankan migrasi
php artisan module:migrate RajaShield

# 3. Jalankan seeder
php artisan module:seed RajaShield

# 4. Sync permissions
php artisan rajashield:sync-permissions --force

# 5. Assign super admin role
php artisan tinker
# $user = User::find(1); $user->assignRole('super-admin');
```

**✅ Selesai!** Login ke admin panel dan cek menu RajaShield.

## 📚 Dokumentasi Lengkap

### 🎯 Untuk Pemula
- 🚀 [**Quick Start Guide**](quick-start.md) - Setup dalam 5 menit
- 📖 [**Instalasi dan Konfigurasi**](instalasi.md) - Panduan instalasi lengkap
- 🎯 [**Panduan Penggunaan**](penggunaan.md) - Cara menggunakan semua fitur

### 💻 Untuk Developer
- 💡 [**Contoh Implementasi**](contoh.md) - Contoh kode dan use cases
- 📚 [**API Reference**](api-reference.md) - Dokumentasi API lengkap
- 🔧 [**Troubleshooting**](troubleshooting.md) - Solusi masalah umum

### 📋 Referensi
- ❓ [**FAQ**](faq.md) - Pertanyaan yang sering diajukan
- 📝 [**Changelog**](changelog.md) - Riwayat perubahan versi
- 🚀 [**Deployment Guide**](deployment.md) - Panduan deployment production

## Kontribusi

Modul ini dikembangkan sebagai bagian dari ekosistem Laravel modular. Untuk kontribusi atau laporan bug, silakan hubungi tim development.

## Lisensi

Modul RajaShield dilisensikan di bawah [MIT License](../LICENSE.md).
