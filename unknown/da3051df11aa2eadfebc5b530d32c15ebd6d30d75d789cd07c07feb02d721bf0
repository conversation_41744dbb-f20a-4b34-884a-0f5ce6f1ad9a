<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class AdminPermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Permission untuk Admin Panel
        $adminPermissions = [
            // CMS permissions
            ['name' => 'admin.cms', 'description' => 'Access CMS', 'group' => 'page'],
            
            // Pengaturan permissions
            ['name' => 'admin.pengaturan.karyawan', 'description' => 'Manage Staff/Karyawan', 'group' => 'page'],
            ['name' => 'admin.pengaturan.kategori', 'description' => 'Manage Categories', 'group' => 'page'],
            ['name' => 'admin.pengaturan.metodepembayaran', 'description' => 'Manage Payment Methods', 'group' => 'page'],
            ['name' => 'admin.pengaturan.toko', 'description' => 'Manage Store/Business', 'group' => 'page'],
            ['name' => 'admin.pengaturan.migrasi', 'description' => 'Data Migration', 'group' => 'page'],
            
            // System permissions
            ['name' => 'admin.system.data', 'description' => 'System Data Management', 'group' => 'page'],
            ['name' => 'admin.system.rute', 'description' => 'Route Management', 'group' => 'page'],
            ['name' => 'admin.system.aplikasi', 'description' => 'Application Management', 'group' => 'page'],
            ['name' => 'admin.system.konfig', 'description' => 'System Configuration', 'group' => 'page'],
            ['name' => 'admin.system.backup', 'description' => 'System Backup', 'group' => 'page'],
        ];

        // Buat permissions jika belum ada
        foreach ($adminPermissions as $permissionData) {
            Permission::firstOrCreate(
                ['name' => $permissionData['name']],
                [
                    'guard_name' => 'web',
                    'group' => $permissionData['group'],
                    'description' => $permissionData['description'],
                ]
            );
        }

        // Assign semua permission ke super-admin
        $superAdminRole = Role::where('name', 'super-admin')->first();
        if ($superAdminRole) {
            $allAdminPermissions = Permission::whereIn('name', collect($adminPermissions)->pluck('name'))->get();
            $superAdminRole->givePermissionTo($allAdminPermissions);
        }

        $this->command->info('Admin permissions berhasil ditambahkan: ' . count($adminPermissions) . ' permissions');
    }
}
