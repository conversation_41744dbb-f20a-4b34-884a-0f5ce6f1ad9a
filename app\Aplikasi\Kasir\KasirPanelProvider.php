<?php

namespace App\Aplikasi\Kasir;

use App\Aplikasi\Kasir\Models\Konfig;
use App\Aplikasi\Kasir\Pages\Dashboard;
use App\Aplikasi\kasir\Resources\PenjualanResource;


use App\Aplikasi\Kasir\Resources\PenjualanShiftResource;
use App\Filament\Resources\MetodePembayaranUtamaResource;
use App\Models\User;
use DutchCodingCompany\FilamentDeveloperLogins\FilamentDeveloperLoginsPlugin;
use Filament\Http\Middleware\Authenticate;
use Filament\Http\Middleware\DisableBladeIconComponents;
use Filament\Http\Middleware\DispatchServingFilamentEvent;
use Filament\Navigation\MenuItem;
use Filament\Navigation\NavigationGroup;
use Filament\Navigation\NavigationItem;
use Filament\Panel;
use Filament\PanelProvider;
use Filament\Support\Colors\Color;
use Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse;
use Illuminate\Cookie\Middleware\EncryptCookies;
use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken;
use Illuminate\Routing\Middleware\SubstituteBindings;
use Illuminate\Session\Middleware\AuthenticateSession;
use Illuminate\Session\Middleware\StartSession;
use Illuminate\Support\Facades\Auth;
use Illuminate\View\Middleware\ShareErrorsFromSession;
use Jeffgreco13\FilamentBreezy\BreezyCore;
use Livewire\Livewire;

class KasirPanelProvider extends PanelProvider
{
    public function panel(Panel $panel): Panel
    {



        return $panel
            ->id('kasir')
            ->path('adminkasir')
            // ->spa()
            // ->login()
            ->login()
            ->registration()
            ->passwordReset()
            ->emailVerification()
            ->profile()
            ->brandName('Aplikasi Kasir')

            ->topNavigation()
            ->pages([
                Dashboard::class,
            ])
            ->globalSearch(false)
            ->viteTheme('resources/css/filament/kasir/theme.css')
            ->navigationGroups([
                NavigationGroup::make('Kasir'),
                NavigationGroup::make('Penjualan'),
                NavigationGroup::make('Shift'),
                NavigationGroup::make('Pengaturan'),
            ])
            ->navigationItems([
                // Dashboard
                NavigationItem::make('Dashboard')
                    ->icon('heroicon-o-home')
                    ->isActiveWhen(fn(): bool => request()->routeIs('filament.kasir.pages.dashboard'))
                    ->url(fn(): string => Dashboard::getUrl())
                    ->sort(1),

                // Aplikasi Kasir
                NavigationItem::make('Kasir')
                    ->icon('heroicon-o-shopping-cart')
                    ->visible($this->cekAkses('adminkasir.kasir'))
                    ->url(
                        function () {
                            if (Konfig::isi('tampilan_kasir') == "minimarket") {
                                return PenjualanResource::getUrl('minimarket');
                            } else {
                                return PenjualanResource::getUrl('resto');
                            }
                        }
                    )->openUrlInNewTab()
                    ->sort(2),

                NavigationItem::make('List Penjualan')
                    ->icon('heroicon-o-chart-bar-square')
                    ->visible($this->cekAkses('adminkasir.penjualan.lihat'))
                    ->group('Penjualan')
                    ->url(fn(): string => PenjualanResource::getUrl('index'))
                    ->sort(2),



                NavigationItem::make('Laporan Penjualan')
                    ->icon('heroicon-o-chart-bar')
                    ->group('Penjualan')
                    ->visible($this->cekAkses('adminkasir.penjualan.laporan'))
                    ->url(fn(): string => PenjualanResource::getUrl('laporan'))
                    ->sort(3),

                NavigationItem::make('Refund')
                    ->icon('heroicon-o-arrow-path')
                    ->group('Penjualan')
                    ->visible($this->cekAkses('adminkasir.refund'))
                    ->url(fn(): string => \App\Aplikasi\Kasir\Resources\RefundResource::getUrl('index'))
                    ->sort(3),

                NavigationItem::make('Shift')
                    ->icon('heroicon-o-arrows-right-left')
                    ->group('Penjualan')
                    ->visible($this->cekAkses('adminkasir.shift'))
                    ->url(fn(): string => \App\Aplikasi\Kasir\Resources\PenjualanShiftResource::getUrl('index'))
                    ->sort(4),

                NavigationItem::make('Produk')
                    ->icon('heroicon-o-cube')
                    ->group('Pengaturan')
                    ->visible($this->cekAkses('adminkasir.pengaturan.produk'))
                    ->url(fn(): string => \App\Aplikasi\Kasir\Resources\ProdukResource::getUrl('index'))
                    ->sort(5),

                NavigationItem::make('Kategori Produk')
                    ->icon('heroicon-o-tag')
                    ->group('Pengaturan')
                    ->visible($this->cekAkses('adminkasir.kategori'))
                    ->url(fn(): string => \App\Aplikasi\Kasir\Resources\ProdukKategoriResource::getUrl('index'))
                    ->sort(6),

                NavigationItem::make('Data Master')
                    ->icon('heroicon-o-document-text')
                    ->group('Pengaturan')
                    ->visible($this->cekAkses('adminkasir.pengaturan.data'))
                    ->url(fn(): string => \App\Aplikasi\Kasir\Pages\DataPage::getUrl())
                    ->sort(7),

                NavigationItem::make('Konfigurasi')
                    ->icon('heroicon-o-adjustments-horizontal')
                    ->group('Pengaturan')
                    ->visible($this->cekAkses('adminkasir.pengaturan.konfig'))
                    ->url(fn(): string => \App\Aplikasi\Kasir\Resources\KonfigResource::getUrl('index'))
                    ->sort(8),




            ])

            ->renderHook(
                'panels::global-search.before',
                fn(): string => view('kasir::infokasir')->render()
            )

            ->resources([
                // Resource lain yang sudah ada
                MetodePembayaranUtamaResource::class,
            ])
            ->widgets([
                \App\Filament\Widgets\RajaDesainerWidget::class,
            ])
            ->discoverResources(in: __DIR__ . '/Resources', for: 'App\\Aplikasi\\Kasir\\Resources')
            ->discoverPages(in: __DIR__ . '/Pages', for: 'App\\Aplikasi\\Kasir\\Pages')
            ->discoverWidgets(in: __DIR__ . '/Widgets', for: 'App\\Aplikasi\\Kasir\\Widgets')
            ->discoverLivewireComponents(in: __DIR__ . '/Livewire', for: 'App\\Aplikasi\\Kasir\\Livewire')
            ->middleware([
                // \App\Http\Middleware\BypassAuth::class, // DISABLED - Bypass autentikasi untuk development
                EncryptCookies::class,
                AddQueuedCookiesToResponse::class,
                StartSession::class,
                AuthenticateSession::class,
                ShareErrorsFromSession::class,
                VerifyCsrfToken::class,
                SubstituteBindings::class,
                DisableBladeIconComponents::class,
                DispatchServingFilamentEvent::class,
            ])
            ->authMiddleware([
                Authenticate::class,
            ])
            ->authGuard('web')
            ->loginRouteSlug('login')
            ->registrationRouteSlug('register')
            ->passwordResetRoutePrefix('password-reset')
            ->passwordResetRequestRouteSlug('request')
            ->passwordResetRouteSlug('reset')
            ->emailVerificationRoutePrefix('email-verification')
            ->emailVerificationPromptRouteSlug('prompt')
            ->emailVerificationRouteSlug('verify')
            ->plugins([


                // \Agencetwogether\HooksHelper\HooksHelperPlugin::make(),
                FilamentDeveloperLoginsPlugin::make()
                    ->enabled()
                    ->users(fn() => User::pluck('email', 'name')->toArray()),
                BreezyCore::make()
                    ->myProfile(
                        shouldRegisterUserMenu: true, // Sets the 'account' link in the panel User Menu (default = true)
                        userMenuLabel: 'My Profile', // Customizes the 'account' link label in the panel User Menu (default = null)
                        shouldRegisterNavigation: false, // Adds a main navigation item for the My Profile page (default = false)
                        navigationGroup: 'Pengaturan', // Sets the navigation group for the My Profile page (default = null)
                        hasAvatars: false, // Enables the avatar upload form component (default = false)
                        slug: 'my-profile' // Sets the slug for the profile page (default = 'my-profile')
                    ),


            ])

        ;
    }


    protected function cekAkses($permission)
    {
        return fn() => Auth::check() && (Auth::user()->hasRole('superadmin') || Auth::user()->can($permission));
    }
}
