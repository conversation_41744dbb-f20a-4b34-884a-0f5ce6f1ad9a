# Panduan Penggunaan RajaShield

## Daftar Isi

1. [Interface Admin Panel](#interface-admin-panel)
2. [Manajemen Roles](#manajemen-roles)
3. [Manajemen Permissions](#manajemen-permissions)
4. [Sync Permissions](#sync-permissions)
5. [Command Line Interface](#command-line-interface)
6. [Penggunaan di Code](#penggunaan-di-code)

## Interface Admin Panel

### Akses Menu RajaShield

Setelah login ke FilamentPHP admin panel, Anda akan melihat menu **RajaShield** dengan sub-menu:

- **Roles** - Manajemen peran pengguna
- **Permissions** - Manajemen izin akses
- **Sync Permissions** - Sinkronisasi permissions

### Navigation Structure

```
🏠 Dashboard
📊 Analytics
🛡️ RajaShield
├── 👥 Roles
├── 🔐 Permissions
└── 🔄 Sync Permissions
```

## Manajemen Roles

### Membuat Role Baru

1. **Akses Roles**: Klik menu **RajaShield > Roles**
2. **Klik Buat Peran**: Tombol di pojok kanan atas
3. **Isi Form**:
   - **Nama**: <PERSON><PERSON> role (contoh: `editor`, `moderator`)
   - **Deskripsi**: Deskripsi role (opsional)

### Mengatur Permissions untuk Role

Form role menggunakan sistem tab untuk organisasi permissions:

#### Tab Resource
Permissions untuk FilamentPHP Resources:
- **view_{resource}** - Melihat record individual
- **view_any_{resource}** - Melihat daftar records
- **create_{resource}** - Membuat record baru
- **update_{resource}** - Mengupdate record
- **delete_{resource}** - Menghapus record
- **delete_any_{resource}** - Bulk delete records

#### Tab Halaman
Permissions untuk FilamentPHP Pages:
- **view_page_{page}** - Akses ke halaman tertentu

#### Tab Widget
Permissions untuk FilamentPHP Widgets:
- **view_widget_{widget}** - Akses ke widget tertentu

### Layout Permissions

Permissions ditampilkan dalam **grid 3-kolom** untuk organisasi yang optimal:

```
┌─────────────┬─────────────┬─────────────┐
│   Column 1  │   Column 2  │   Column 3  │
│             │             │             │
│ ☑ view      │ ☑ create    │ ☑ delete    │
│ ☑ view_any  │ ☑ update    │ ☑ delete_any│
└─────────────┴─────────────┴─────────────┘
```

### Edit Role

1. **Akses List Roles**: Menu **RajaShield > Roles**
2. **Klik Edit**: Icon pensil pada row role
3. **Update Permissions**: Centang/uncentang permissions sesuai kebutuhan
4. **Simpan**: Klik tombol **Simpan**

### Hapus Role

1. **Akses List Roles**: Menu **RajaShield > Roles**
2. **Klik Delete**: Icon trash pada row role
3. **Konfirmasi**: Konfirmasi penghapusan

⚠️ **Peringatan**: Role `super-admin` tidak dapat dihapus karena merupakan role sistem.

## Manajemen Permissions

### Melihat Daftar Permissions

1. **Akses Permissions**: Menu **RajaShield > Permissions**
2. **Filter by Group**: Gunakan filter untuk melihat permissions berdasarkan group:
   - **resource** - Permissions untuk Resources
   - **page** - Permissions untuk Pages
   - **widget** - Permissions untuk Widgets

### Struktur Permission

Setiap permission memiliki:
- **Name**: Nama permission (contoh: `view_any_users`)
- **Description**: Deskripsi permission
- **Group**: Kategori permission (resource/page/widget)
- **Guard**: Guard yang digunakan (default: web)

### Membuat Permission Manual

1. **Klik Buat Izin**: Tombol di pojok kanan atas
2. **Isi Form**:
   - **Name**: Nama permission (gunakan format snake_case)
   - **Description**: Deskripsi permission
   - **Group**: Kategori permission
   - **Guard**: Guard yang digunakan

### Edit Permission

1. **Klik Edit**: Icon pensil pada row permission
2. **Update Data**: Ubah nama, deskripsi, atau group
3. **Simpan**: Klik tombol **Simpan**

## Sync Permissions

### Mengapa Perlu Sync?

Sync permissions diperlukan ketika:
- ✅ Menambah Resource/Page/Widget baru
- ✅ Menghapus Resource/Page/Widget
- ✅ Setelah update modul atau aplikasi
- ✅ Setelah deployment
- ✅ Maintenance rutin

### Akses Sync Permissions

1. **Menu Sync**: **RajaShield > Sync Permissions**
2. **Quick Sync**: Tombol **Sync Permissions** di halaman Roles/Permissions

### Interface Sync Permissions

#### Status Dashboard
Menampilkan informasi:
- **Current Permissions**: Jumlah permissions saat ini
- **New Permissions Found**: Permissions baru yang terdeteksi
- **To Add**: Permissions yang akan ditambahkan
- **To Remove**: Permissions yang akan dihapus

#### Preview Changes
Menampilkan preview permissions yang akan:
- ➕ **Ditambahkan**: Permissions baru dari entities baru
- ➖ **Dihapus**: Permissions lama yang tidak terpakai

#### Grouping by Entity
Permissions dikelompokkan berdasarkan:
- **Resources**: Grouped by resource name
- **Pages**: Grouped by page name  
- **Widgets**: Grouped by widget name

### Melakukan Sync

1. **Review Changes**: Periksa permissions yang akan ditambah/dihapus
2. **Klik Sync Permissions**: Tombol hijau di bawah
3. **Konfirmasi**: Konfirmasi perubahan
4. **Wait**: Tunggu proses selesai

### Hasil Sync

Setelah sync berhasil:
- ✅ Permissions baru ditambahkan
- ✅ Permissions lama dihapus
- ✅ Super-admin mendapat permissions baru
- ✅ Notifikasi sukses ditampilkan

## Command Line Interface

### Sync Permissions Command

#### Preview Sync (Dry Run)
```bash
php artisan rajashield:sync-permissions --dry-run
```

Output:
```
🔄 Starting permission sync...
📊 Sync Summary:
+-----------------------+-------+
| Type                  | Count |
+-----------------------+-------+
| Current Permissions   | 124   |
| New Permissions Found | 113   |
| To Add                | 1     |
| To Remove             | 12    |
+-----------------------+-------+

➕ Permissions to ADD:
  • view_new_resource

➖ Permissions to REMOVE:
  • view_old_resource

🔍 Dry run completed. No changes were made.
```

#### Sync dengan Konfirmasi
```bash
php artisan rajashield:sync-permissions
```

#### Force Sync (Tanpa Konfirmasi)
```bash
php artisan rajashield:sync-permissions --force
```

### Module Commands

#### Enable/Disable Module
```bash
# Enable modul
php artisan module:enable RajaShield

# Disable modul
php artisan module:disable RajaShield

# Cek status modul
php artisan module:list
```

#### Migration Commands
```bash
# Jalankan migrasi
php artisan module:migrate RajaShield

# Rollback migrasi
php artisan module:migrate-rollback RajaShield

# Reset migrasi
php artisan module:migrate-reset RajaShield
```

#### Seeder Commands
```bash
# Jalankan seeder
php artisan module:seed RajaShield

# Jalankan seeder specific
php artisan db:seed --class="Modules\RajaShield\Database\Seeders\RajaShieldSeeder"
```

## Penggunaan di Code

### Cek Role

```php
// Cek apakah user memiliki role tertentu
if (auth()->user()->hasRole('admin')) {
    // User adalah admin
}

// Cek multiple roles
if (auth()->user()->hasAnyRole(['admin', 'editor'])) {
    // User adalah admin atau editor
}

// Cek semua roles
if (auth()->user()->hasAllRoles(['admin', 'editor'])) {
    // User memiliki kedua role
}
```

### Cek Permission

```php
// Cek permission specific
if (auth()->user()->can('view_any_users')) {
    // User dapat melihat daftar users
}

// Cek permission menggunakan hasPermissionTo
if (auth()->user()->hasPermissionTo('create_posts')) {
    // User dapat membuat post
}

// Cek multiple permissions
if (auth()->user()->hasAnyPermission(['edit_posts', 'delete_posts'])) {
    // User dapat edit atau delete posts
}
```

### Assign Role/Permission

```php
// Assign role ke user
$user = User::find(1);
$user->assignRole('editor');

// Assign multiple roles
$user->assignRole(['editor', 'moderator']);

// Remove role
$user->removeRole('editor');

// Sync roles (replace semua role)
$user->syncRoles(['admin']);
```

### Direct Permission Assignment

```php
// Give permission langsung ke user
$user->givePermissionTo('edit_articles');

// Revoke permission
$user->revokePermissionTo('edit_articles');

// Sync permissions
$user->syncPermissions(['edit_articles', 'delete_articles']);
```

### Middleware Usage

```php
// Di routes
Route::group(['middleware' => ['role:admin']], function () {
    // Routes untuk admin saja
});

Route::group(['middleware' => ['permission:edit_posts']], function () {
    // Routes untuk user dengan permission edit_posts
});

// Multiple conditions
Route::group(['middleware' => ['role_or_permission:admin|edit_posts']], function () {
    // Routes untuk admin ATAU user dengan permission edit_posts
});
```

### Blade Directives

```blade
{{-- Cek role --}}
@role('admin')
    <p>Anda adalah admin</p>
@endrole

{{-- Cek permission --}}
@can('edit_posts')
    <a href="{{ route('posts.edit', $post) }}">Edit</a>
@endcan

{{-- Cek multiple roles --}}
@hasanyrole('admin|editor')
    <p>Anda adalah admin atau editor</p>
@endhasanyrole

{{-- Cek permission untuk model --}}
@can('update', $post)
    <a href="{{ route('posts.edit', $post) }}">Edit Post</a>
@endcan
```

### FilamentPHP Integration

```php
// Di Resource
public static function canViewAny(): bool
{
    return auth()->user()->can('view_any_posts');
}

public static function canCreate(): bool
{
    return auth()->user()->can('create_posts');
}

public static function canEdit(Model $record): bool
{
    return auth()->user()->can('update_posts');
}

public static function canDelete(Model $record): bool
{
    return auth()->user()->can('delete_posts');
}
```

Lanjutkan ke [Contoh Implementasi](contoh.md) untuk melihat contoh kode lengkap dan use cases.
