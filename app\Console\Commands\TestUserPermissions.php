<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;

class TestUserPermissions extends Command
{
    protected $signature = 'user:test-permissions {user_id}';
    protected $description = 'Test user permissions';

    public function handle()
    {
        $userId = $this->argument('user_id');
        
        $user = User::find($userId);
        if (!$user) {
            $this->error("User with ID {$userId} not found");
            return 1;
        }

        $this->info("=== USER PERMISSION TEST ===");
        $this->info("User: {$user->name} (ID: {$user->id})");
        $this->info("Email: {$user->email}");
        
        $this->info("\n=== ROLES ===");
        $roles = $user->roles->pluck('name')->toArray();
        if (empty($roles)) {
            $this->warn("No roles assigned");
        } else {
            foreach ($roles as $role) {
                $this->info("✓ {$role}");
            }
        }

        $this->info("\n=== ROLE CHECKS ===");
        $this->info("Has super-admin role: " . ($user->hasRole('super-admin') ? '✓ YES' : '✗ NO'));
        $this->info("Has admin role: " . ($user->hasRole('admin') ? '✓ YES' : '✗ NO'));
        $this->info("Has member role: " . ($user->hasRole('member') ? '✓ YES' : '✗ NO'));

        $this->info("\n=== PERMISSION CHECKS ===");
        $testPermissions = [
            'admin.pengaturan.karyawan',
            'view_any_users',
            'create_users',
            'update_users',
            'delete_users',
            'admin.cms',
            'admin.system.konfig',
        ];

        foreach ($testPermissions as $permission) {
            $hasPermission = $user->can($permission);
            $this->info(($hasPermission ? '✓' : '✗') . " {$permission}");
        }

        $this->info("\n=== ALL USER PERMISSIONS ===");
        $allPermissions = $user->getAllPermissions()->pluck('name')->toArray();
        if (empty($allPermissions)) {
            $this->warn("No permissions found");
        } else {
            $this->info("Total permissions: " . count($allPermissions));
            foreach ($allPermissions as $permission) {
                $this->info("- {$permission}");
            }
        }
        
        return 0;
    }
}
